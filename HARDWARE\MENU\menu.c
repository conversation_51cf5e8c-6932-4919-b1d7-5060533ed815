#include "menu.h"
#include "delay.h"
#include "oled.h"
#include "led.h" 



/**********************************************************

                     以下为一级菜单

**********************************************************/
int menu1()           
{
	int flag=1;         //记录你的选择
  OLED_ShowString(1,3,"blue");//显示一个字符号串
	OLED_ShowString(2,3,"black");//显示一个字符号串
 // OLED_ShowString(1,1,"*");
	while(1)
	{
		if(KEY1==0)       //***下一项***
		{		
			delay_ms(10);	  //防抖
			while(KEY1==0) ; //按下后不松手，则停在这
			delay_ms(10);	  //防抖
			flag++;
			if(flag==3) flag=1;			
		}
		
		if(KEY4==0)       //***确认***
		{
			delay_ms(10);	  //防抖
			while(KEY4==0); //按下后不松手，则停在这
			delay_ms(10);	  //防抖			
			OLED_Clear();   //清屏
			return(flag);   //返回你的选择
		}
		switch(flag)      //光标移动
		{
			case 1:
			{
			  OLED_ShowString(3,1," ");
				OLED_ShowString(2,1," ");
				OLED_ShowString(1,1,"*");
			}break;
			case 2:
			{
				OLED_ShowString(1,1," ");
				OLED_ShowString(3,1," ");
				OLED_ShowString(2,1,"*");
			}break;
		}		
		
	}
}
/**********************************************************

                     以下为二级菜单

**********************************************************/
int menu11()           
{
	int flag=1;         //记录你的选择
  OLED_ShowString(1,3,"near");//显示一个字符号串
	OLED_ShowString(2,3,"centre");//显示一个字符号串
	OLED_ShowString(3,3,"far");//显示一个字符号串
//			sprintf(OledString, "hw:%.2d",Tracking_DO); 
//			OLED_ShowString(4,1,OledString);
	while(1)
	{
		if(KEY1==0)       //***下一项***
		{		
			delay_ms(10);	  //防抖
			while(KEY1==0); //按下后不松手，则停在这
			delay_ms(10);	  //防抖
			flag++;
			if(flag==4) flag=1;			
		}
		
		if(KEY4==0)       //***确认***
		{
			delay_ms(10);	  //防抖
			while(KEY4==0); //按下后不松手，则停在这
			delay_ms(10);	  //防抖			
			OLED_Clear();   //清屏
			return(flag);   //返回你的选择
		}
		switch(flag)      //光标移动
		{
			case 1:
			{
			  OLED_ShowString(3,1," ");
				OLED_ShowString(2,1," ");
				OLED_ShowString(1,1,"*");
			}break;
			case 2:
			{
				OLED_ShowString(1,1," ");
				OLED_ShowString(3,1," ");
				OLED_ShowString(2,1,"*");
			}break;
			case 3:
			{
				OLED_ShowString(1,1," ");
				OLED_ShowString(2,1," ");
				OLED_ShowString(3,1,"*");				
			}break;
		}		
		
	}
}
int menu12()           
{
	int flag=1;         //记录你的选择
	OLED_ShowString(1,3,"double centre");//显示一个字符号串
	OLED_ShowString(2,3,"double far");//显示一个字符号串
                    
	while(1)
	{
		if(KEY1==0)       //***下一项***
		{		
			delay_ms(10);	  //防抖
			while(KEY1==0); //按下后不松手，则停在这
			delay_ms(10);	  //防抖
			flag++;
			if(flag==4) flag=1;			
		}
		
		if(KEY4==0)       //***确认***
		{
			delay_ms(10);	  //防抖
			while(KEY4==0); //按下后不松手，则停在这
			delay_ms(10);	  //防抖			
			OLED_Clear();   //清屏
			return(flag);   //返回你的选择
		}
		switch(flag)      //光标移动
		{
			case 1:
			{
			  OLED_ShowString(3,1," ");
				OLED_ShowString(2,1," ");
				OLED_ShowString(1,1,"*");
			}break;
			case 2:
			{
				OLED_ShowString(1,1," ");
				OLED_ShowString(3,1," ");
				OLED_ShowString(2,1,"*");
			}break;
		
		
		}		
		
	}
}
