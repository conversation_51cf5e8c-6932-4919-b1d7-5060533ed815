#ifndef __LED_H
#define __LED_H
#include "sys.h"

//////////////////////////////////////////////////////////////////////////////////	 
//本程序只供学习使用，未经作者许可，不得用于其它任何用途
//ALIENTEK STM32F407开发板
//LED驱动代码	   
//正点原子@ALIENTEK
//技术论坛:www.openedv.com
//创建日期:2014/5/2
//版本：V1.0
//版权所有，盗版必究。
//Copyright(C) 广州市星翼电子科技有限公司 2014-2024
//All rights reserved									  
////////////////////////////////////////////////////////////////////////////////// 	


//LED端口定义
#define LED0 PCout(13)	// DS0
#define KEY1 PFout(1)   //按键1
#define KEY2 PFout(2)   //按键2
#define KEY3 PFout(3)   //按键3
#define KEY4 PFout(4)   //按键4


#define led_R GPIO_ReadInputDataBit(GPIOC,GPIO_Pin_13)
#define led_W(n) GPIO_WriteBit(GPIOC,GPIO_Pin_13,(BitAction)(n))
#define hw_R GPIO_ReadInputDataBit(GPIOB,GPIO_Pin_3)//读取光对管引脚电平

void LED_Init(void);//初始化	
void key_init(void);//初始化

void hw_oled(void);

#endif
