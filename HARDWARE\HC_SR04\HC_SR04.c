#include "hc_sr04.h"//超声波传感器，使用了定时器5，未测试
#include "delay.h"
#include "math.h"

int overcount=0; 



//GPIO??????
void TIM5_Int_Init(void)
{
		GPIO_InitTypeDef GPIO_InitStruct;
		TIM_TimeBaseInitTypeDef TIM_TimeBaseStructure;
		NVIC_InitTypeDef NVIC_InitStructure;

		RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM5, ENABLE); 
		RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);

		GPIO_InitStruct.GPIO_Mode=GPIO_Mode_OUT;
		GPIO_InitStruct.GPIO_OType = GPIO_OType_PP;
		GPIO_InitStruct.GPIO_Pin=GPIO_Pin_3;
		GPIO_InitStruct.GPIO_Speed=GPIO_Speed_50MHz;
		GPIO_Init(GPIOA,&GPIO_InitStruct);

		GPIO_InitStruct.GPIO_Mode=GPIO_Mode_IN;
		GPIO_InitStruct.GPIO_PuPd = GPIO_PuPd_NOPULL;
		GPIO_InitStruct.GPIO_OType = GPIO_OType_OD;
		GPIO_InitStruct.GPIO_Pin=GPIO_Pin_2;
		GPIO_Init(GPIOA,&GPIO_InitStruct);

		TIM_TimeBaseStructure.TIM_Period = 999; 
		TIM_TimeBaseStructure.TIM_Prescaler =83; 
		TIM_TimeBaseStructure.TIM_ClockDivision = TIM_CKD_DIV1;
		TIM_TimeBaseStructure.TIM_CounterMode = TIM_CounterMode_Up; 
		TIM_TimeBaseInit(TIM5, &TIM_TimeBaseStructure);

		TIM_ITConfig(TIM5,TIM_IT_Update,ENABLE );

		NVIC_InitStructure.NVIC_IRQChannel = TIM5_IRQn;
		NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0;
		NVIC_InitStructure.NVIC_IRQChannelSubPriority = 3;
		NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
		NVIC_Init(&NVIC_InitStructure);

		TIM_Cmd(TIM5, DISABLE);
}

float Senor_Using(void) 
{
		float length,sum=0;
		u16 tim;
		unsigned int w=0;
		while(w!=5)
 {
		PAout(3)=1; 
		delay_us(20); 
		PAout(3)=0; 
		while(GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_2)==RESET);
		TIM_Cmd(TIM5,ENABLE);
		w+=1; 
		while(GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_2)==SET);
		TIM_Cmd(TIM5,DISABLE);
		tim=TIM_GetCounter(TIM5); 
		length=(tim+overcount*1000)/58.0; 
		sum=length+sum;
		TIM5->CNT=0; 
		overcount=0; 
		delay_ms(100);
 }
		length=sum/5;//???
		return length;
}



void TIM5_IRQHandler(void)
{
    if (TIM_GetITStatus(TIM5,TIM_IT_Update)!= RESET) 
   {
			TIM_ClearITPendingBit(TIM5, TIM_IT_Update ); 
			overcount++;
   }
}

