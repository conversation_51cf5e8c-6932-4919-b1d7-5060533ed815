#include "stm32f4xx.h"
#include "TCRT5000.h"

int hw5[5]={0};
int s1,s2,s3,s4,s5;
float oput;
float error;

void TCRT5000_Init(void)
{
		
  GPIO_InitTypeDef  GPIO_InitStructure;

  RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB,ENABLE);//使能GPIOB时钟
 
  GPIO_InitStructure.GPIO_Pin = GPIO_Pin_3|GPIO_Pin_10|GPIO_Pin_11|GPIO_Pin_12|GPIO_Pin_13; //对应引脚
  GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN;//普通输入模式
  GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;//100M
  GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_NOPULL  ;//
  GPIO_Init(GPIOB, &GPIO_InitStructure);//初始化
}




void TCRT5000_read(void)
{
		
  

	hw5[0]=GPIO_ReadInputDataBit(GPIOB,GPIO_Pin_3);
	hw5[1]=GPIO_ReadInputDataBit(GPIOB,GPIO_Pin_10);
	hw5[2]=GPIO_ReadInputDataBit(GPIOB,GPIO_Pin_11);
	hw5[3]=GPIO_ReadInputDataBit(GPIOB,GPIO_Pin_12);
	hw5[4]=GPIO_ReadInputDataBit(GPIOB,GPIO_Pin_13);

	  
}














