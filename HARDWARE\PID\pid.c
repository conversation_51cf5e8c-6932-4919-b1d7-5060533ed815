#include "pid.h"

//定义一个结构体类型变量
tPid pidMotor1Speed;      //左轮速度环
tPid pidMotor2Speed;      //右轮速度环
tPid pidTurnSpeed;        //转向环
tPid pidLocationSpeed;	  //位置环

float filt_err;
float last_filt_err;
//给结构体类型变量赋初值
void PID_init()
{

	pidMotor1Speed.actual_val=0.0;      //左轮速度环
	pidMotor1Speed.target_val=0;
	pidMotor1Speed.err=0.0;
	pidMotor1Speed.err_last=0.0;
	pidMotor1Speed.err_sum=0.0;
	pidMotor1Speed.Kp=100;             //此参数只适配MG513x电机，其他电机请自行调整
	pidMotor1Speed.Ki=10;
	pidMotor1Speed.Kd=0;
	
	pidMotor2Speed.actual_val=0.0;     //右轮速度环
	pidMotor2Speed.target_val=0;
	pidMotor2Speed.err=0.0;
	pidMotor2Speed.err_last=0.0;
	pidMotor2Speed.err_sum=0.0;
	pidMotor2Speed.Kp=100;
	pidMotor2Speed.Ki=10;
	pidMotor2Speed.Kd=0;
	 
	pidTurnSpeed.actual_val=0.0;      //转向环
	pidTurnSpeed.target_val=90.0;
	pidTurnSpeed.err=0.0;
	pidTurnSpeed.err_last=0.0;
	pidTurnSpeed.err_sum=0.0;
	pidTurnSpeed.Kp=0.2;
	pidTurnSpeed.Ki=0;
	pidTurnSpeed.Kd=0;//70
	
	pidLocationSpeed.actual_val=0.0;      //位置环
	pidLocationSpeed.target_val=0.0;
	pidLocationSpeed.err=0.0;
	pidLocationSpeed.err_last=0.0;
	pidLocationSpeed.err_sum=0.0;
	pidLocationSpeed.Kp=4.8;
	pidLocationSpeed.Ki=0.004;
	pidLocationSpeed.Kd=0;
	

}
//比例p调节控制函数
float P_realize(tPid * pid,float actual_val)
{
	pid->actual_val = actual_val;//传递真实值
	pid->err = pid->target_val - pid->actual_val;//当前误差=目标值-真实值
	//比例控制调节   输出=Kp*当前误差
	pid->actual_val = pid->Kp*pid->err;
	return pid->actual_val;
}
//比例P 积分I 控制函数`	
float PI_realize(tPid * pid,float actual_val)
{
	pid->actual_val = actual_val;//传递真实值
	pid->err = pid->target_val - pid->actual_val;//当前误差=目标值-真实值
	pid->err_sum += pid->err;//误差累计值 = 当前误差累计和
	//使用PI控制 输出=Kp*当前误差+Ki*误差累计值
	pid->actual_val = pid->Kp*pid->err + pid->Ki*pid->err_sum;
	
	return pid->actual_val;
}
// PID控制函数
float PID_realize(tPid * pid,float actual_val)
{
	float a = 0.3;
	pid->actual_val = actual_val;//传递真实值
	pid->err = pid->target_val - pid->actual_val;////当前误差=目标值-真实值
	filt_err = a*pid->err + (1-a)*last_filt_err;   //滤波
	pid->err_sum += pid->err;//误差累计值 = 当前误差累计和
	last_filt_err = filt_err;
	//使用PID控制 输出 = Kp*当前误差  +  Ki*误差累计值 + Kd*(当前误差-上次误差)
	pid->actual_val = pid->Kp*pid->err + pid->Ki*pid->err_sum + pid->Kd*(pid->err - pid->err_last);
	//保存上次误差: 这次误差赋值给上次误差
	pid->err_last = pid->err;
	
	return pid->actual_val;
}


float PD_realize(tPid * pid,float actual_val)
{
	pid->actual_val = actual_val;
	pid->err = pid->target_val - pid->actual_val;
	
	if(pid->err>180) pid->err = pid->err - 360;   
	if(pid->err<-180) pid->err = pid->err + 360;
	
	pid->err_sum += pid->err;
	
	pid->actual_val = pid->Kp*pid->err + pid->Ki*pid->err_sum + pid->Kd*(pid->err - pid->err_last);
	
	pid->err_last = pid->err;
	
	
	return pid->actual_val;
}
