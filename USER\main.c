#include "sys.h"
#include "stm32f4xx.h"
#include "delay.h"
#include "led.h"
#include "key.h"
#include "timer.h"
#include "oled.h"
#include "pwm.h"
#include "control.h"
#include "encoder.h"
#include "motor.h"
#include "Serial.h"
#include "pid.h"

//#include "mpu6050.h"
//#include "inv_mpu.h"
//#include "inv_mpu_dmp_motion_driver.h" 

extern void TIM1_PWM_Init(u32 arr,u32 psc);
extern void TIM9_PWM_Init(void);
extern void KEY_Init(void);
extern u8 KEY_Scan(u8 mode);

///////////////////////////////////////////////////////
///////////////////////////////////////////////////////
extern tPid pidMotor1Speed;      
extern tPid pidMotor2Speed;      
extern tPid pidTurnSpeed;        
extern float Speed1;
extern float Speed2;
float pitch,roll,yaw; 
char OledString[100];
int key=0;




extern int cross,stop,cross_flag,num,Flag;
int w = 0;
extern int turn_flag;

// 新增调试变量声明
extern int cross_master_num;
extern int remote_stage;
extern int target_room;
extern int straight_after_turn;
extern int stop_flag;


int main(void)
{ 
	NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
	delay_init(168);             
	LED_Init();				           
	KEY_Init();                  
	TIM1_PWM_Init(1000-1,840-1);     
	TIM9_PWM_Init();
	Control_Init();                 
	Encoder_Init_TIM4();
	Encoder_Init_TIM2();            
	Serial_Init();                
	OLED_Init();                     
	PID_init();                     

//	MPU_Init();			
//  while(mpu_dmp_init())
//	{                       
//		delay_ms(200);
//		OLED_ShowString(1,1,"mpu6050 error");
//	}
	
    TIM3_Int_Init(100-1,8400-1);	   



	/*oled静态显示*/
	OLED_ShowString(1, 1, "state:");

	while(1)
	{
		key=KEY_Scan(1);
		hw_oled();

		// 第1行：标题后跟当前房间状态
		OLED_ShowNum(1,14,w,2);

		// 第2行：路口和转向状态
		sprintf(OledString, "C:%d->%d T:%d", cross_flag, cross_master_num, turn_flag);
		OLED_ShowString(2,1,OledString);

		// 第3行：远端状态和停止信息
		sprintf(OledString, "RS:%d SF:%d ST:%d", remote_stage, stop_flag, straight_after_turn);
		OLED_ShowString(3,1,OledString);

		// 第4行：速度和目标房间（动态显示）
		if(target_room > 0)
		{
			sprintf(OledString, "TR:%d L:%.1f R:%.1f", target_room, Speed1, Speed2);
			OLED_ShowString(4,1,OledString);
		}
		else
		{
			sprintf(OledString, "L:%.1f R:%.1f S:%d", Speed1, Speed2, stop);
			OLED_ShowString(4,1,OledString);
		}

		// 数字识别处理 - 优化版本
		int detected_num = num_deal(num);
		if(detected_num >= 1 && detected_num <= 8 && Flag == 1 && w == 0)
		{
			cross_flag = 0;
			w = detected_num;  // 直接赋值识别到的数字
		}
		
		

	}
}
