#include "timer.h"
#include "led.h"
#include "delay.h"
#include "encoder.h"
#include "pid.h"
#include "motor.h"
//#include "mpu6050.h"
//#include "inv_mpu.h"
//#include "inv_mpu_dmp_motion_driver.h"
#include "Serial.h"
#include "stdlib.h"
#include "pwm.h"







extern float Speed1;
extern float Speed2;
int Timeout;
int lastTime = 0;
extern tPid pidMotor1Speed;      //左轮速度环
extern tPid pidMotor2Speed;      //右轮速度环
extern int turnsign;
extern int turntime;
extern int walksign;
extern int walktime;
/////////////////////////////
int turn_flag = 0;
extern int w, cross_flag, stop, Flag;
extern void Servo_Set (float Angle);
extern int stop_flag ,num;

// 远端房间(5-8)的多级T字路口导航状态
int remote_stage = 0;       // 0:未开始, 1:已完成第3个T字路口转向,等待下一个T字路口, 2:完成所有转向
int target_room = 0;        // 当前目标房间号码(5-8)

// 转向后直行状态
int straight_after_turn = 0; // 0:不需要直行, 1:需要转向后直行

// 定时器计数器变量
int cross_count = 0;         // 路口处理计数器
int turn_count = 0;          // 转向计数器
int stop_count = 0;          // 停止处理计数器
int straight_count = 0;      // 转向后直行计数器


void TIM3_Int_Init(u16 arr, u16 psc)
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3, ENABLE); ///使能TIM3时钟

    TIM_TimeBaseInitStructure.TIM_Period = arr;     //自动重装载值
    TIM_TimeBaseInitStructure.TIM_Prescaler = psc; //定时器分频
    TIM_TimeBaseInitStructure.TIM_CounterMode = TIM_CounterMode_Up; //向上计数模式
    TIM_TimeBaseInitStructure.TIM_ClockDivision = TIM_CKD_DIV1;

    TIM_TimeBaseInit(TIM3, &TIM_TimeBaseInitStructure); //初始化TIM3

    TIM_ITConfig(TIM3, TIM_IT_Update, ENABLE); //允许定时器3更新中断
    TIM_Cmd(TIM3, ENABLE); //使能定时器3

    NVIC_InitStructure.NVIC_IRQChannel = TIM3_IRQn; //定时器3中断
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0x00; //抢占优先级1
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0x03; //子优先级3
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

}

int cross_master_num = 0;

//定时器3中断服务函数
void TIM3_IRQHandler(void)                     //10ms一次中断
{
    if (TIM_GetITStatus(TIM3, TIM_IT_Update) == SET) //溢出中断
    {
		
////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////
		
        Timeout++;
        /* 数据测量 */
        Speed1 = read_Speed1();
        Speed2 = read_Speed2();
		Servo_Set(96);
		
///////////////////////////////////////////////////////////	

		/*视觉运行函数*/
		if(cross_flag == 0)
		{
			Car_Run(9, 9);  // 正常巡线送药
		}

		/*路口检测识别处理*/
		if(cross_flag == 1)
		{
			cross_count++;
			Car_Run(3,3);
			if(cross_count > 50)  // 500ms延时
			{
				MotorB_Set(0,0);

				// 路口分类逻辑
				if((w < 3) && (w >= 1) && cross_flag == 1)	cross_master_num = 1;  // 第1个十字路口
				if((w < 5) && (w >= 3) && cross_flag == 1)	cross_master_num = 2;  // 第2个十字路口
				if((w >= 5) && cross_flag == 1)
				{
					if(remote_stage == 0)  // 第一次到达远端区域
					{
						cross_master_num = 3;  // 第3个T字路口
						target_room = w;       // 记录目标房间
					}
					else if(remote_stage == 1)  // 第二次到达T字路口
					{
						cross_master_num = 4;  // 第4个T字路口(左侧或右侧分支的T字路口)
					}
				}

				cross_flag = 255;
				cross_count = 0;
			}
		}

		// 数字识别处理
		int detected_num = num_deal(num);
		if(detected_num >= 1 && detected_num <= 8 && Flag == 1 && w == 0)
		{
			cross_flag = 0;
			w = detected_num;  // 直接赋值识别到的数字
		}

////////////////**************近端房间1-2：第1个十字路口************/////////////////////////
		// 房间1：第1个十字路口左转直行到房间停止线
		if(w == 1 && cross_master_num == 1 && stop_flag == 0)
		{
			turn_flag = 1;  // 去程：第1个十字路口左转
		}
		if(w == 1 && cross_master_num == 1 && stop_flag == 1)
		{
			turn_flag = 2;  // 返程：从房间1出来右转回主路
		}

		// 房间2：第1个十字路口右转直行到房间停止线
		if(w == 2 && cross_master_num == 1 && stop_flag == 0)
		{
			turn_flag = 2;  // 去程：第1个十字路口右转
		}
		if(w == 2 && cross_master_num == 1 && stop_flag == 1)
		{
			turn_flag = 1;  // 返程：从房间2出来左转回主路
		}

////////////////**************中端房间3-4：第2个十字路口************/////////////////////////
		// 房间3：第2个十字路口左转直行到房间停止线
		if(w == 3 && cross_master_num == 2 && stop_flag == 0)
		{
			turn_flag = 1;  // 去程：第2个十字路口左转
		}
		if(w == 3 && cross_master_num == 2 && stop_flag == 1)
		{
			turn_flag = 2;  // 返程：从房间3出来右转回主路
		}

		// 房间4：第2个十字路口右转直行到房间停止线
		if(w == 4 && cross_master_num == 2 && stop_flag == 0)
		{
			turn_flag = 2;  // 去程：第2个十字路口右转
		}
		if(w == 4 && cross_master_num == 2 && stop_flag == 1)
		{
			turn_flag = 1;  // 返程：从房间4出来左转回主路
		}

////////////////**************远端房间5-8 第一阶段：第3个T字路口************/////////////////////////
		// 房间5,7：第3个T字路口左转进入左侧分支
		if((target_room == 5 || target_room == 7) && cross_master_num == 3 && stop_flag == 0)
		{
			turn_flag = 1;  // 去程：第3个T字路口左转
			remote_stage = 1;  // 标记已完成第一阶段转向
		}
		// 房间6,8：第3个T字路口右转进入右侧分支
		if((target_room == 6 || target_room == 8) && cross_master_num == 3 && stop_flag == 0)
		{
			turn_flag = 2;  // 去程：第3个T字路口右转
			remote_stage = 1;  // 标记已完成第一阶段转向
		}

////////////////**************远端房间5-8 第二阶段：分支T字路口************/////////////////////////
		// 房间5：左侧分支T字路口右转
		if(target_room == 5 && cross_master_num == 4 && stop_flag == 0)
		{
			turn_flag = 2;  // 去程：左侧T字路口右转到房间5
			remote_stage = 2;  // 标记完成所有转向
		}
		// 房间7：左侧分支T字路口左转
		if(target_room == 7 && cross_master_num == 4 && stop_flag == 0)
		{
			turn_flag = 1;  // 去程：左侧T字路口左转到房间7
			remote_stage = 2;  // 标记完成所有转向
		}
		// 房间6：右侧分支T字路口右转
		if(target_room == 6 && cross_master_num == 4 && stop_flag == 0)
		{
			turn_flag = 2;  // 去程：右侧T字路口右转到房间6
			remote_stage = 2;  // 标记完成所有转向
		}
		// 房间8：右侧分支T字路口左转
		if(target_room == 8 && cross_master_num == 4 && stop_flag == 0)
		{
			turn_flag = 1;  // 去程：右侧T字路口左转到房间8
			remote_stage = 2;  // 标记完成所有转向
		}

////////////////**************远端房间5-8 返程处理************/////////////////////////
		// 返程时重置状态并按相反方向返回
		if((target_room >= 5 && target_room <= 8) && stop_flag == 1)
		{
			if(remote_stage == 2)  // 从房间出来，第一次转向
			{
				// 房间5,6：右转出房间
				if(target_room == 5 || target_room == 6) turn_flag = 2;
				// 房间7,8：左转出房间
				if(target_room == 7 || target_room == 8) turn_flag = 1;
				remote_stage = 1;  // 回到第一阶段
			}
			else if(remote_stage == 1)  // 从分支回到主路，第二次转向
			{
				// 左侧分支(房间5,7)：右转回主路
				if(target_room == 5 || target_room == 7) turn_flag = 2;
				// 右侧分支(房间6,8)：左转回主路
				if(target_room == 6 || target_room == 8) turn_flag = 1;
				remote_stage = 0;  // 重置状态
				target_room = 0;   // 清除目标房间
			}
		}

		/*左转条件函数*/
		if(turn_flag == 1)
		{
			turn_count++;
			motorPidSetSpeed(3,-3);
			if(turn_count >20)
			{
				MotorB_Set(0,0);
				straight_after_turn = 1;  // 转向完成后需要直行
				turn_count = 0;
				turn_flag = 0;
			}
		}
		/*右转条件函数*/
		if(turn_flag == 2)
		{
			turn_count++;
			motorPidSetSpeed(-3,3);
			if(turn_count >20)
			{
				MotorB_Set(0,0);
				straight_after_turn = 1;  // 转向完成后需要直行
				turn_count = 0;
				turn_flag = 0;
			}
		}

        /* 定时器中断指示灯 */
        if (Timeout % 200 == 0)
        {
            LED0 = !LED0; //DS1翻转
            Timeout = 0;
        }

    } // 关闭 if (TIM_GetITStatus(TIM3, TIM_IT_Update) == SET)

interrupt_end:
    TIM_ClearITPendingBit(TIM3, TIM_IT_Update); //清除中断标志位
} // 关闭 void TIM3_IRQHandler(void)
