#include "timer.h"
#include "led.h"
#include "delay.h"
#include "encoder.h"
#include "pid.h"
#include "motor.h"
//#include "mpu6050.h"
//#include "inv_mpu.h"
//#include "inv_mpu_dmp_motion_driver.h"
#include "Serial.h"
#include "stdlib.h"
#include "pwm.h"







extern float Speed1;
extern float Speed2;
int Timeout;
int lastTime = 0;
extern tPid pidMotor1Speed;      //左轮速度环
extern tPid pidMotor2Speed;      //右轮速度环
extern int turnsign;
extern int turntime;
extern int walksign;
extern int walktime;
/////////////////////////////
int turn_flag = 0;
extern int w, cross_flag, stop, Flag;
extern void Servo_Set (float Angle);
extern int stop_flag ,num;

// 远端房间(5-8)的多级T字路口导航状态
int remote_stage = 0;       // 0:未开始, 1:已完成第3个T字路口转向,等待下一个T字路口, 2:完成所有转向
int target_room = 0;        // 当前目标房间号码(5-8)

// 转向后直行状态
int straight_after_turn = 0; // 0:不需要直行, 1:需要转向后直行

// 定时器计数器变量
int cross_count = 0;         // 路口处理计数器
int turn_count = 0;          // 转向计数器
int stop_count = 0;          // 停止处理计数器
int straight_count = 0;      // 转向后直行计数器


void TIM3_Int_Init(u16 arr, u16 psc)
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3, ENABLE); ///使能TIM3时钟

    TIM_TimeBaseInitStructure.TIM_Period = arr;     //自动重装载值
    TIM_TimeBaseInitStructure.TIM_Prescaler = psc; //定时器分频
    TIM_TimeBaseInitStructure.TIM_CounterMode = TIM_CounterMode_Up; //向上计数模式
    TIM_TimeBaseInitStructure.TIM_ClockDivision = TIM_CKD_DIV1;

    TIM_TimeBaseInit(TIM3, &TIM_TimeBaseInitStructure); //初始化TIM3

    TIM_ITConfig(TIM3, TIM_IT_Update, ENABLE); //允许定时器3更新中断
    TIM_Cmd(TIM3, ENABLE); //使能定时器3

    NVIC_InitStructure.NVIC_IRQChannel = TIM3_IRQn; //定时器3中断
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0x00; //抢占优先级1
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0x03; //子优先级3
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

}

int cross_master_num = 0;

//定时器3中断服务函数
void TIM3_IRQHandler(void)                     //10ms一次中断
{
    if (TIM_GetITStatus(TIM3, TIM_IT_Update) == SET) //溢出中断
    {
		
////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////
		
        Timeout++;
        /* 数据测量 */
        Speed1 = read_Speed1();
        Speed2 = read_Speed2();
		Servo_Set(96);
		
///////////////////////////////////////////////////////////	

		/*视觉运行函数*/
		if(cross_flag == 0)
		{
			Car_Run(9, 9);  // 正常巡线送药
			// cross_count = 0;  // 注释掉，不要每次都重置计数器
		}


		/*路口检测识别处理*/
		if(1)  // 强制测试延时逻辑
		{
			cross_count++;
			Car_Run(3,3);
			if(cross_count > 50)  // 500ms延时测试
			{
				// 强制停车 - 多种方式确保停止
				MotorB_Set(0,0);
				motorPidSetSpeed(0,0);

				// 延时测试完成，直接跳出中断，不执行任何后续逻辑
				goto interrupt_end;
			}
		}

		// 延时测试完成，其他逻辑暂时注释

		// 数字识别处理 - 简化版本
		int detected_num = num_deal(num);
		if(detected_num >= 1 && detected_num <= 8 && Flag == 1 && w == 0)
		{
			cross_flag = 0;
			w = detected_num;  // 直接赋值识别到的数字
		}

		// 其他复杂逻辑暂时注释，只保留基本结构

        /* 定时器中断指示灯 */
        if (Timeout % 200 == 0)
        {
            LED0 = !LED0; //DS1翻转
            Timeout = 0;
        }

    } // 关闭 if (TIM_GetITStatus(TIM3, TIM_IT_Update) == SET)

interrupt_end:
    TIM_ClearITPendingBit(TIM3, TIM_IT_Update); //清除中断标志位
} // 关闭 void TIM3_IRQHandler(void)
		




//////////////////////////////////////////////////////////////////////////
//////////////////////////////////////////////////////////////////////////

//        /* 定时器中断指示灯 */
//        if (Timeout % 200 == 0)
//        {
//            LED0 = !LED0; //DS1翻转

//            Timeout = 0;
//        }

//    }

//interrupt_end:
//    TIM_ClearITPendingBit(TIM3, TIM_IT_Update); //清除中断标志位
//}
