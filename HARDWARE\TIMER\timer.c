#include "timer.h"
#include "led.h"
#include "delay.h"
#include "encoder.h"
#include "pid.h"
#include "motor.h"
//#include "mpu6050.h"
//#include "inv_mpu.h"
//#include "inv_mpu_dmp_motion_driver.h"
#include "Serial.h"
#include "stdlib.h"
#include "pwm.h"







extern float Speed1;
extern float Speed2;
int Timeout;
int lastTime = 0;
extern tPid pidMotor1Speed;      //左轮速度环
extern tPid pidMotor2Speed;      //右轮速度环
extern int turnsign;
extern int turntime;
extern int walksign;
extern int walktime;
/////////////////////////////
int turn_flag = 0;
extern int w,cross_f;
extern void Servo_Set (float Angle);
extern int cross_flag;
extern int stop_flag;


void TIM3_Int_Init(u16 arr, u16 psc)
{
    TIM_TimeBaseInitTypeDef TIM_TimeBaseInitStructure;
    NVIC_InitTypeDef NVIC_InitStructure;

    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3, ENABLE); ///使能TIM3时钟

    TIM_TimeBaseInitStructure.TIM_Period = arr;     //自动重装载值
    TIM_TimeBaseInitStructure.TIM_Prescaler = psc; //定时器分频
    TIM_TimeBaseInitStructure.TIM_CounterMode = TIM_CounterMode_Up; //向上计数模式
    TIM_TimeBaseInitStructure.TIM_ClockDivision = TIM_CKD_DIV1;

    TIM_TimeBaseInit(TIM3, &TIM_TimeBaseInitStructure); //初始化TIM3

    TIM_ITConfig(TIM3, TIM_IT_Update, ENABLE); //允许定时器3更新中断
    TIM_Cmd(TIM3, ENABLE); //使能定时器3

    NVIC_InitStructure.NVIC_IRQChannel = TIM3_IRQn; //定时器3中断
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 0x00; //抢占优先级1
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 0x03; //子优先级3
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_Init(&NVIC_InitStructure);

}

int cross_master_num = 0;

//定时器3中断服务函数
void TIM3_IRQHandler(void)                     //10ms一次中断
{	
	static int count = 0;
    if (TIM_GetITStatus(TIM3, TIM_IT_Update) == SET) //溢出中断
    {
		
////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////
		
        Timeout++;
        /* 数据测量 */
        Speed1 = read_Speed1();
        Speed2 = read_Speed2();
		Servo_Set(96);
		
///////////////////////////////////////////////////////////	

		/*视觉运行函数*/
		if(cross_flag == 0)
		{
			Car_Run(9, 9);  // 正常巡线送药
			count = 0;
		}
		
		
		/*路口检测识别处理*/
		if(cross_flag == 1)
		{	
			
			count++;
			Car_Run(3,3);
			if(count >90)
			{
				MotorB_Set(0,0);
				if((w < 3) && (w >= 1) && cross_flag == 1)	cross_master_num = 1;
				if((w < 5) && (w >= 3) && cross_flag == 1)	cross_master_num = 2;
				if((w >= 5) && cross_flag == 1)		cross_master_num = 3;
		
				cross_flag = 255;
				
////////////////////////////////////////////////////////////////////////////
////////////////**************近端数字1************/////////////////////////		
				if(w == 1 && cross_master_num == 1 && stop_flag == 0)
				{
					turn_flag = 1;
				}
				if(w == 1 && cross_master_num == 1 && stop_flag == 1)
				{
					turn_flag = 2;
				}
/////////////////////////////////////////////////////////////////////////////
////////////////**************近端数字2************//////////////////////////
				if(w == 2 && cross_master_num == 1 && stop_flag == 0)
				{
					turn_flag = 1;
				}
				if(w == 2 && cross_master_num == 1 && stop_flag == 1)
				{
					turn_flag = 2;
				}
////////////////////////////////////////////////////////////////////////////
////////////////**************中端数字3************/////////////////////////
				if(w == 3 && cross_master_num == 2 && stop_flag == 0)
				{
					turn_flag = 1;
				}
				if(w == 3 && cross_master_num == 2 && stop_flag == 1)
				{
					turn_flag = 2;
				}
////////////////////////////////////////////////////////////////////////////
////////////////**************中端数字4************/////////////////////////
				if(w == 4 && cross_master_num == 2 && stop_flag == 0)
				{
					turn_flag = 2;
				}
				if(w == 4 && cross_master_num == 2 && stop_flag == 1)
				{
					turn_flag = 1;
				}
				
			}
			count = 0;
		}
		/*左转条件函数*/
		if(turn_flag == 1)
		{
			count++;
			motorPidSetSpeed(3,-3);
			if(count >20)
			{
				MotorB_Set(0,0);
				cross_flag = 0;
				count = 0;
				turn_flag = 0;
			}
			
		}
		/*右转条件函数*/
		if(turn_flag == 2)
		{
			count++;
			motorPidSetSpeed(-3,3);
			if(count >20)
			{
				MotorB_Set(0,0);
				cross_flag = 0;
				count = 0;
				turn_flag = 0;
			}
		}
		/*小车停止条件*/
		if(stop_flag == 1)
		{
			MotorB_Set(0,0);
			if(Flag == 2)
			{	
				count++;
				motorPidSetSpeed(2,-2);
				if(count > 20)
				{
					MotorB_Set(0,0);
					cross_flag =0;
					count = 0;
				}
			}
		}			
			


		
		
		
		
		




////////////////////////////////////////////////////////////////////////
////////////////////////////////////////////////////////////////////////

        /* 定时器中断指示灯 */
        if (Timeout % 200 == 0)
        {
            LED0 = !LED0; //DS1翻转

            Timeout = 0;
        }

    }
    TIM_ClearITPendingBit(TIM3, TIM_IT_Update); //清除中断标志位
}
