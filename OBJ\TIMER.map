Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    main.o(.text) refers to delay.o(.text) for delay_init
    main.o(.text) refers to led.o(.text) for LED_Init
    main.o(.text) refers to key.o(.text) for KEY_Init
    main.o(.text) refers to pwm.o(.text) for TIM1_PWM_Init
    main.o(.text) refers to control.o(.text) for Control_Init
    main.o(.text) refers to encoder.o(.text) for Encoder_Init_TIM4
    main.o(.text) refers to serial.o(.text) for Serial_Init
    main.o(.text) refers to oled.o(.text) for OLED_Init
    main.o(.text) refers to pid.o(.text) for PID_init
    main.o(.text) refers to timer.o(.text) for TIM3_Int_Init
    main.o(.text) refers to f2d.o(.text) for __aeabi_f2d
    main.o(.text) refers to printfa.o(i.__0sprintf) for __2sprintf
    main.o(.text) refers to encoder.o(.data) for Speed1
    main.o(.text) refers to serial.o(.data) for cross
    main.o(.text) refers to led.o(.data) for Flag
    main.o(.text) refers to main.o(.data) for .data
    main.o(.text) refers to main.o(.bss) for .bss
    main.o(.text) refers to timer.o(.data) for turn_flag
    system_stm32f4xx.o(.text) refers to system_stm32f4xx.o(.data) for .data
    led.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    led.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    led.o(.text) refers to oled.o(.text) for OLED_ShowString
    led.o(.text) refers to led.o(.data) for .data
    timer.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    timer.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    timer.o(.text) refers to misc.o(.text) for NVIC_Init
    timer.o(.text) refers to encoder.o(.text) for read_Speed1
    timer.o(.text) refers to pwm.o(.text) for Servo_Set
    timer.o(.text) refers to motor.o(.text) for Car_Run
    timer.o(.text) refers to timer.o(.data) for .data
    timer.o(.text) refers to encoder.o(.data) for Speed1
    timer.o(.text) refers to serial.o(.data) for cross_flag
    timer.o(.text) refers to main.o(.data) for w
    timer.o(.text) refers to led.o(.data) for Flag
    control.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    control.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    encoder.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    encoder.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    encoder.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    encoder.o(.text) refers to encoder.o(.data) for .data
    motor.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_SetCompare3
    motor.o(.text) refers to pid.o(.text) for PID_realize
    motor.o(.text) refers to pid.o(.bss) for pidMotor1Speed
    motor.o(.text) refers to encoder.o(.data) for Speed2
    motor.o(.text) refers to serial.o(.data) for arm
    oled.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    oled.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    oled.o(.text) refers to delay.o(.text) for delay_us
    oled.o(.text) refers to oled.o(.constdata) for .constdata
    pwm.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphClockCmd
    pwm.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    pwm.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    serial.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphClockCmd
    serial.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    serial.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    serial.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    serial.o(.text) refers to serial.o(.data) for .data
    serial.o(.text) refers to serial.o(.bss) for .bss
    menu.o(.text) refers to oled.o(.text) for OLED_ShowString
    menu.o(.text) refers to delay.o(.text) for delay_ms
    pid.o(.text) refers to pid.o(.bss) for .bss
    pid.o(.text) refers to pid.o(.data) for .data
    key.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    key.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    key.o(.text) refers to delay.o(.text) for delay_ms
    key.o(.text) refers to key.o(.data) for .data
    myiic.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    myiic.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    myiic.o(.text) refers to delay.o(.text) for delay_us
    mpu6050.o(.text) refers to myiic.o(.text) for IIC_Start
    mpu6050.o(.text) refers to delay.o(.text) for delay_ms
    mpu6050.o(.text) refers to dflti.o(.text) for __aeabi_i2d
    mpu6050.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    mpu6050.o(.text) refers to dadd.o(.text) for __aeabi_dadd
    mpu6050.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    inv_mpu.o(.text) refers to mpu6050.o(.text) for MPU_Write_Len
    inv_mpu.o(.text) refers to printfa.o(i.__0printf) for __2printf
    inv_mpu.o(.text) refers to delay.o(.text) for delay_ms
    inv_mpu.o(.text) refers to inv_mpu.o(.data) for .data
    inv_mpu.o(.text) refers to inv_mpu.o(.conststring) for .conststring
    inv_mpu.o(.text) refers to ldiv.o(.text) for __aeabi_ldivmod
    inv_mpu.o(.text) refers to memcmp.o(.text) for memcmp
    inv_mpu.o(.text) refers to inv_mpu_dmp_motion_driver.o(.text) for dmp_set_gyro_bias
    inv_mpu.o(.text) refers to myiic.o(.text) for IIC_Init
    inv_mpu.o(.text) refers to f2d.o(.text) for __aeabi_f2d
    inv_mpu.o(.text) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    inv_mpu.o(.text) refers to dmul.o(.text) for __aeabi_dmul
    inv_mpu.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    inv_mpu.o(.text) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    inv_mpu.o(.data) refers to inv_mpu.o(.constdata) for reg
    inv_mpu.o(.data) refers to inv_mpu.o(.constdata) for hw
    inv_mpu.o(.data) refers to inv_mpu.o(.constdata) for test
    inv_mpu_dmp_motion_driver.o(.text) refers to inv_mpu.o(.text) for mpu_load_firmware
    inv_mpu_dmp_motion_driver.o(.text) refers to inv_mpu_dmp_motion_driver.o(.constdata) for .constdata
    inv_mpu_dmp_motion_driver.o(.text) refers to inv_mpu_dmp_motion_driver.o(.bss) for .bss
    inv_mpu_dmp_motion_driver.o(.text) refers to memseta.o(.text) for __aeabi_memset
    hc_sr04.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB1PeriphClockCmd
    hc_sr04.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    hc_sr04.o(.text) refers to stm32f4xx_tim.o(.text) for TIM_TimeBaseInit
    hc_sr04.o(.text) refers to misc.o(.text) for NVIC_Init
    hc_sr04.o(.text) refers to delay.o(.text) for delay_us
    hc_sr04.o(.text) refers to dflti.o(.text) for __aeabi_i2d
    hc_sr04.o(.text) refers to ddiv.o(.text) for __aeabi_ddiv
    hc_sr04.o(.text) refers to d2f.o(.text) for __aeabi_d2f
    hc_sr04.o(.text) refers to hc_sr04.o(.data) for .data
    tcs34725.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    tcs34725.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    tcs34725.o(.text) refers to delay.o(.text) for delay_us
    tcrt5000.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    tcrt5000.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_Init
    tcrt5000.o(.text) refers to tcrt5000.o(.bss) for .bss
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to delay.o(.data) for .data
    usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphClockCmd
    usart.o(.text) refers to stm32f4xx_gpio.o(.text) for GPIO_PinAFConfig
    usart.o(.text) refers to stm32f4xx_usart.o(.text) for USART_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    startup_stm32f40_41xxx.o(RESET) refers to startup_stm32f40_41xxx.o(.text) for Reset_Handler
    startup_stm32f40_41xxx.o(RESET) refers to stm32f4xx_it.o(.text) for NMI_Handler
    startup_stm32f40_41xxx.o(RESET) refers to timer.o(.text) for TIM3_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to serial.o(.text) for USART1_IRQHandler
    startup_stm32f40_41xxx.o(RESET) refers to hc_sr04.o(.text) for TIM5_IRQHandler
    startup_stm32f40_41xxx.o(.text) refers to system_stm32f4xx.o(.text) for SystemInit
    startup_stm32f40_41xxx.o(.text) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    stm32f4xx_gpio.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_AHB1PeriphResetCmd
    stm32f4xx_rcc.o(.text) refers to stm32f4xx_rcc.o(.data) for .data
    stm32f4xx_syscfg.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_tim.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f4xx_usart.o(.text) refers to stm32f4xx_rcc.o(.text) for RCC_APB2PeriphResetCmd
    asin.o(i.__hardfp_asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin.o(i.__hardfp_asin) refers to dmul.o(.text) for __aeabi_dmul
    asin.o(i.__hardfp_asin) refers to dadd.o(.text) for __aeabi_dadd
    asin.o(i.__hardfp_asin) refers to errno.o(i.__set_errno) for __set_errno
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_invalid) for __mathlib_dbl_invalid
    asin.o(i.__hardfp_asin) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    asin.o(i.__hardfp_asin) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    asin.o(i.__hardfp_asin) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin.o(i.__hardfp_asin) refers to ddiv.o(.text) for __aeabi_ddiv
    asin.o(i.__hardfp_asin) refers to fabs.o(i.fabs) for fabs
    asin.o(i.__hardfp_asin) refers to sqrt.o(i.sqrt) for sqrt
    asin.o(i.__hardfp_asin) refers to asin.o(.constdata) for .constdata
    asin.o(i.__softfp_asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.__softfp_asin) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    asin.o(i.asin) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin.o(i.asin) refers to asin.o(i.__hardfp_asin) for __hardfp_asin
    asin.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____hardfp_asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____hardfp_asin$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    asin_x.o(i.____hardfp_asin$lsc) refers to dmul.o(.text) for __aeabi_dmul
    asin_x.o(i.____hardfp_asin$lsc) refers to dadd.o(.text) for __aeabi_dadd
    asin_x.o(i.____hardfp_asin$lsc) refers to errno.o(i.__set_errno) for __set_errno
    asin_x.o(i.____hardfp_asin$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    asin_x.o(i.____hardfp_asin$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    asin_x.o(i.____hardfp_asin$lsc) refers to fabs.o(i.fabs) for fabs
    asin_x.o(i.____hardfp_asin$lsc) refers to sqrt.o(i.sqrt) for sqrt
    asin_x.o(i.____hardfp_asin$lsc) refers to asin_x.o(.constdata) for .constdata
    asin_x.o(i.____softfp_asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.____softfp_asin$lsc) refers to asin_x.o(i.____hardfp_asin$lsc) for ____hardfp_asin$lsc
    asin_x.o(i.__asin$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    asin_x.o(i.__asin$lsc) refers to asin_x.o(i.____hardfp_asin$lsc) for ____hardfp_asin$lsc
    asin_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__hardfp_atan2) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2.o(i.__hardfp_atan2) refers to atan.o(i.atan) for atan
    atan2.o(i.__hardfp_atan2) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2.o(i.__hardfp_atan2) refers to fabs.o(i.fabs) for fabs
    atan2.o(i.__hardfp_atan2) refers to dadd.o(.text) for __aeabi_dsub
    atan2.o(i.__hardfp_atan2) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2.o(i.__hardfp_atan2) refers to errno.o(i.__set_errno) for __set_errno
    atan2.o(i.__hardfp_atan2) refers to qnan.o(.constdata) for __mathlib_zero
    atan2.o(i.__softfp_atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.__softfp_atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2.o(i.atan2) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2.o(i.atan2) refers to atan2.o(i.__hardfp_atan2) for __hardfp_atan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dunder.o(i.__mathlib_dbl_infnan2) for __mathlib_dbl_infnan2
    atan2_x.o(i.____hardfp_atan2$lsc) refers to atan.o(i.atan) for atan
    atan2_x.o(i.____hardfp_atan2$lsc) refers to errno.o(i.__set_errno) for __set_errno
    atan2_x.o(i.____hardfp_atan2$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan2_x.o(i.____hardfp_atan2$lsc) refers to fabs.o(i.fabs) for fabs
    atan2_x.o(i.____hardfp_atan2$lsc) refers to dadd.o(.text) for __aeabi_dsub
    atan2_x.o(i.____hardfp_atan2$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmpeq
    atan2_x.o(i.____hardfp_atan2$lsc) refers to qnan.o(.constdata) for __mathlib_zero
    atan2_x.o(i.____softfp_atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.____softfp_atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    atan2_x.o(i.__atan2$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan2_x.o(i.__atan2$lsc) refers to atan2_x.o(i.____hardfp_atan2$lsc) for ____hardfp_atan2$lsc
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry10a.o(.ARM.Collect$$$$0000000F) for __rt_final_cpp
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry11a.o(.ARM.Collect$$$$00000011) for __rt_final_exit
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry12b.o(.ARM.Collect$$$$0000000E) for __rt_lib_shutdown_fini
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry7b.o(.ARM.Collect$$$$00000008) for _main_clock
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry8b.o(.ARM.Collect$$$$0000000A) for _main_cpp_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry9a.o(.ARM.Collect$$$$0000000B) for _main_init
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry5.o(.ARM.Collect$$$$00000004) for _main_scatterload
    entry.o(.ARM.Collect$$$$00000000) refers (Special) to entry2.o(.ARM.Collect$$$$00000001) for _main_stk
    ldiv.o(.text) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfb.o(i.__0fprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0fprintf$bare) refers to usart.o(.text) for fputc
    printfb.o(i.__0printf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0printf$bare) refers to usart.o(.text) for fputc
    printfb.o(i.__0printf$bare) refers to usart.o(.data) for __stdout
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0snprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0sprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printfb.o(i.__0vfprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vfprintf$bare) refers to usart.o(.text) for fputc
    printfb.o(i.__0vprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vprintf$bare) refers to usart.o(.text) for fputc
    printfb.o(i.__0vprintf$bare) refers to usart.o(.data) for __stdout
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsnprintf$bare) refers to printfb.o(i._snputc) for _snputc
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._printf_core) for _printf_core
    printfb.o(i.__0vsprintf$bare) refers to printfb.o(i._sputc) for _sputc
    printf0.o(i.__0fprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0fprintf$0) refers to usart.o(.text) for fputc
    printf0.o(i.__0printf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0printf$0) refers to usart.o(.text) for fputc
    printf0.o(i.__0printf$0) refers to usart.o(.data) for __stdout
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0snprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0sprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf0.o(i.__0vfprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vfprintf$0) refers to usart.o(.text) for fputc
    printf0.o(i.__0vprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vprintf$0) refers to usart.o(.text) for fputc
    printf0.o(i.__0vprintf$0) refers to usart.o(.data) for __stdout
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsnprintf$0) refers to printf0.o(i._snputc) for _snputc
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._printf_core) for _printf_core
    printf0.o(i.__0vsprintf$0) refers to printf0.o(i._sputc) for _sputc
    printf1.o(i.__0fprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0fprintf$1) refers to usart.o(.text) for fputc
    printf1.o(i.__0printf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0printf$1) refers to usart.o(.text) for fputc
    printf1.o(i.__0printf$1) refers to usart.o(.data) for __stdout
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0snprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0sprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i.__0vfprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vfprintf$1) refers to usart.o(.text) for fputc
    printf1.o(i.__0vprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vprintf$1) refers to usart.o(.text) for fputc
    printf1.o(i.__0vprintf$1) refers to usart.o(.data) for __stdout
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsnprintf$1) refers to printf1.o(i._snputc) for _snputc
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._printf_core) for _printf_core
    printf1.o(i.__0vsprintf$1) refers to printf1.o(i._sputc) for _sputc
    printf1.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf2.o(i.__0fprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0fprintf$2) refers to usart.o(.text) for fputc
    printf2.o(i.__0printf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0printf$2) refers to usart.o(.text) for fputc
    printf2.o(i.__0printf$2) refers to usart.o(.data) for __stdout
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0snprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0sprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf2.o(i.__0vfprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vfprintf$2) refers to usart.o(.text) for fputc
    printf2.o(i.__0vprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vprintf$2) refers to usart.o(.text) for fputc
    printf2.o(i.__0vprintf$2) refers to usart.o(.data) for __stdout
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsnprintf$2) refers to printf2.o(i._snputc) for _snputc
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._printf_core) for _printf_core
    printf2.o(i.__0vsprintf$2) refers to printf2.o(i._sputc) for _sputc
    printf3.o(i.__0fprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0fprintf$3) refers to usart.o(.text) for fputc
    printf3.o(i.__0printf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0printf$3) refers to usart.o(.text) for fputc
    printf3.o(i.__0printf$3) refers to usart.o(.data) for __stdout
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0snprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0sprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i.__0vfprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vfprintf$3) refers to usart.o(.text) for fputc
    printf3.o(i.__0vprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vprintf$3) refers to usart.o(.text) for fputc
    printf3.o(i.__0vprintf$3) refers to usart.o(.data) for __stdout
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsnprintf$3) refers to printf3.o(i._snputc) for _snputc
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._printf_core) for _printf_core
    printf3.o(i.__0vsprintf$3) refers to printf3.o(i._sputc) for _sputc
    printf3.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf4.o(i.__0fprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0fprintf$4) refers to usart.o(.text) for fputc
    printf4.o(i.__0printf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0printf$4) refers to usart.o(.text) for fputc
    printf4.o(i.__0printf$4) refers to usart.o(.data) for __stdout
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0snprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0sprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i.__0vfprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vfprintf$4) refers to usart.o(.text) for fputc
    printf4.o(i.__0vprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vprintf$4) refers to usart.o(.text) for fputc
    printf4.o(i.__0vprintf$4) refers to usart.o(.data) for __stdout
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsnprintf$4) refers to printf4.o(i._snputc) for _snputc
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._printf_core) for _printf_core
    printf4.o(i.__0vsprintf$4) refers to printf4.o(i._sputc) for _sputc
    printf4.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf5.o(i.__0fprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0fprintf$5) refers to usart.o(.text) for fputc
    printf5.o(i.__0printf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0printf$5) refers to usart.o(.text) for fputc
    printf5.o(i.__0printf$5) refers to usart.o(.data) for __stdout
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0snprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0sprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i.__0vfprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vfprintf$5) refers to usart.o(.text) for fputc
    printf5.o(i.__0vprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vprintf$5) refers to usart.o(.text) for fputc
    printf5.o(i.__0vprintf$5) refers to usart.o(.data) for __stdout
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsnprintf$5) refers to printf5.o(i._snputc) for _snputc
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._printf_core) for _printf_core
    printf5.o(i.__0vsprintf$5) refers to printf5.o(i._sputc) for _sputc
    printf5.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf6.o(i.__0fprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0fprintf$6) refers to usart.o(.text) for fputc
    printf6.o(i.__0printf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0printf$6) refers to usart.o(.text) for fputc
    printf6.o(i.__0printf$6) refers to usart.o(.data) for __stdout
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0snprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0sprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i.__0vfprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vfprintf$6) refers to usart.o(.text) for fputc
    printf6.o(i.__0vprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vprintf$6) refers to usart.o(.text) for fputc
    printf6.o(i.__0vprintf$6) refers to usart.o(.data) for __stdout
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsnprintf$6) refers to printf6.o(i._snputc) for _snputc
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._printf_core) for _printf_core
    printf6.o(i.__0vsprintf$6) refers to printf6.o(i._sputc) for _sputc
    printf6.o(i._printf_core) refers to printf6.o(i._printf_pre_padding) for _printf_pre_padding
    printf6.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printf6.o(i._printf_core) refers to printf6.o(i._printf_post_padding) for _printf_post_padding
    printf7.o(i.__0fprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0fprintf$7) refers to usart.o(.text) for fputc
    printf7.o(i.__0printf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0printf$7) refers to usart.o(.text) for fputc
    printf7.o(i.__0printf$7) refers to usart.o(.data) for __stdout
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0snprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0sprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i.__0vfprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vfprintf$7) refers to usart.o(.text) for fputc
    printf7.o(i.__0vprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vprintf$7) refers to usart.o(.text) for fputc
    printf7.o(i.__0vprintf$7) refers to usart.o(.data) for __stdout
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsnprintf$7) refers to printf7.o(i._snputc) for _snputc
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._printf_core) for _printf_core
    printf7.o(i.__0vsprintf$7) refers to printf7.o(i._sputc) for _sputc
    printf7.o(i._printf_core) refers to printf7.o(i._printf_pre_padding) for _printf_pre_padding
    printf7.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf7.o(i._printf_core) refers to printf7.o(i._printf_post_padding) for _printf_post_padding
    printf8.o(i.__0fprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0fprintf$8) refers to usart.o(.text) for fputc
    printf8.o(i.__0printf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0printf$8) refers to usart.o(.text) for fputc
    printf8.o(i.__0printf$8) refers to usart.o(.data) for __stdout
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0snprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0sprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i.__0vfprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vfprintf$8) refers to usart.o(.text) for fputc
    printf8.o(i.__0vprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vprintf$8) refers to usart.o(.text) for fputc
    printf8.o(i.__0vprintf$8) refers to usart.o(.data) for __stdout
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsnprintf$8) refers to printf8.o(i._snputc) for _snputc
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._printf_core) for _printf_core
    printf8.o(i.__0vsprintf$8) refers to printf8.o(i._sputc) for _sputc
    printf8.o(i._printf_core) refers to printf8.o(i._printf_pre_padding) for _printf_pre_padding
    printf8.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printf8.o(i._printf_core) refers to printf8.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i.__0fprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0fprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0fprintf) refers to usart.o(.text) for fputc
    printfa.o(i.__0printf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0printf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0printf) refers to usart.o(.text) for fputc
    printfa.o(i.__0printf) refers to usart.o(.data) for __stdout
    printfa.o(i.__0snprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0snprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0snprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0sprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0sprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0sprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i.__0vfprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vfprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vfprintf) refers to usart.o(.text) for fputc
    printfa.o(i.__0vprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vprintf) refers to usart.o(.text) for fputc
    printfa.o(i.__0vprintf) refers to usart.o(.data) for __stdout
    printfa.o(i.__0vsnprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsnprintf) refers to printfa.o(i._snputc) for _snputc
    printfa.o(i.__0vsprintf) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i.__0vsprintf) refers to printfa.o(i._printf_core) for _printf_core
    printfa.o(i.__0vsprintf) refers to printfa.o(i._sputc) for _sputc
    printfa.o(i._fp_digits) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._fp_digits) refers to dmul.o(.text) for __aeabi_dmul
    printfa.o(i._fp_digits) refers to ddiv.o(.text) for __aeabi_ddiv
    printfa.o(i._fp_digits) refers to cdrcmple.o(.text) for __aeabi_cdrcmple
    printfa.o(i._fp_digits) refers to dadd.o(.text) for __aeabi_dadd
    printfa.o(i._fp_digits) refers to dfixul.o(.text) for __aeabi_d2ulz
    printfa.o(i._fp_digits) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_core) refers to printfa.o(i._printf_pre_padding) for _printf_pre_padding
    printfa.o(i._printf_core) refers to uldiv.o(.text) for __aeabi_uldivmod
    printfa.o(i._printf_core) refers to printfa.o(i._printf_post_padding) for _printf_post_padding
    printfa.o(i._printf_core) refers to printfa.o(i._fp_digits) for _fp_digits
    printfa.o(i._printf_core) refers to uidiv.o(.text) for __aeabi_uidivmod
    printfa.o(i._printf_post_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._printf_pre_padding) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._snputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    printfa.o(i._sputc) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dadd.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    dadd.o(.text) refers to llsshr.o(.text) for __aeabi_lasr
    dadd.o(.text) refers to depilogue.o(.text) for _double_epilogue
    dmul.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dmul.o(.text) refers to depilogue.o(.text) for _double_epilogue
    ddiv.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    ddiv.o(.text) refers to depilogue.o(.text) for _double_round
    dflti.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    dflti.o(.text) refers to depilogue.o(.text) for _double_epilogue
    f2d.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers (Special) to iusefp.o(.text) for __I$use$fp
    d2f.o(.text) refers to fepilogue.o(.text) for _float_round
    atan.o(i.__hardfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan.o(i.__hardfp_atan) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    atan.o(i.__hardfp_atan) refers to dunder.o(i.__mathlib_dbl_underflow) for __mathlib_dbl_underflow
    atan.o(i.__hardfp_atan) refers to fabs.o(i.fabs) for fabs
    atan.o(i.__hardfp_atan) refers to dadd.o(.text) for __aeabi_dadd
    atan.o(i.__hardfp_atan) refers to dmul.o(.text) for __aeabi_dmul
    atan.o(i.__hardfp_atan) refers to ddiv.o(.text) for __aeabi_ddiv
    atan.o(i.__hardfp_atan) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan.o(i.__hardfp_atan) refers to atan.o(.constdata) for .constdata
    atan.o(i.__softfp_atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.__softfp_atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(i.atan) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan.o(i.atan) refers to atan.o(i.__hardfp_atan) for __hardfp_atan
    atan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____hardfp_atan$lsc) refers to dunder.o(i.__mathlib_dbl_infnan) for __mathlib_dbl_infnan
    atan_x.o(i.____hardfp_atan$lsc) refers to fabs.o(i.fabs) for fabs
    atan_x.o(i.____hardfp_atan$lsc) refers to dadd.o(.text) for __aeabi_dadd
    atan_x.o(i.____hardfp_atan$lsc) refers to dmul.o(.text) for __aeabi_dmul
    atan_x.o(i.____hardfp_atan$lsc) refers to ddiv.o(.text) for __aeabi_ddiv
    atan_x.o(i.____hardfp_atan$lsc) refers to poly.o(i.__kernel_poly) for __kernel_poly
    atan_x.o(i.____hardfp_atan$lsc) refers to atan_x.o(.constdata) for .constdata
    atan_x.o(i.____softfp_atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.____softfp_atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(i.__atan$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    atan_x.o(i.__atan$lsc) refers to atan_x.o(i.____hardfp_atan$lsc) for ____hardfp_atan$lsc
    atan_x.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    dunder.o(i.__mathlib_dbl_divzero) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_infnan) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_infnan2) refers to dadd.o(.text) for __aeabi_dadd
    dunder.o(i.__mathlib_dbl_invalid) refers to ddiv.o(.text) for __aeabi_ddiv
    dunder.o(i.__mathlib_dbl_overflow) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_posinfnan) refers to dmul.o(.text) for __aeabi_dmul
    dunder.o(i.__mathlib_dbl_underflow) refers to dmul.o(.text) for __aeabi_dmul
    fabs.o(i.__hardfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.__softfp_fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fabs.o(i.fabs) refers (Special) to iusefp.o(.text) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers (Special) to iusefp.o(.text) for __I$use$fp
    poly.o(i.__kernel_poly) refers to dmul.o(.text) for __aeabi_dmul
    poly.o(i.__kernel_poly) refers to dadd.o(.text) for __aeabi_dadd
    qnan.o(.constdata) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__hardfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__hardfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.__softfp_sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.__softfp_sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.__softfp_sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt.o(i.sqrt) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt.o(i.sqrt) refers to dsqrt.o(.text) for _dsqrt
    sqrt.o(i.sqrt) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____hardfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.____softfp_sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.____softfp_sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    sqrt_x.o(i.__sqrt$lsc) refers (Special) to iusefp.o(.text) for __I$use$fp
    sqrt_x.o(i.__sqrt$lsc) refers to cdcmple.o(.text) for __aeabi_cdcmple
    sqrt_x.o(i.__sqrt$lsc) refers to errno.o(i.__set_errno) for __set_errno
    sqrt_x.o(i.__sqrt$lsc) refers to dsqrt.o(.text) for _dsqrt
    entry2.o(.ARM.Collect$$$$00000001) refers to entry2.o(.ARM.Collect$$$$00002712) for __lit__00000000
    entry2.o(.ARM.Collect$$$$00002712) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to startup_stm32f40_41xxx.o(STACK) for __initial_sp
    entry2.o(__vectab_stack_and_reset_area) refers to entry.o(.ARM.Collect$$$$00000000) for __main
    entry5.o(.ARM.Collect$$$$00000004) refers to init.o(.text) for __scatterload
    entry9a.o(.ARM.Collect$$$$0000000B) refers to main.o(.text) for main
    entry9b.o(.ARM.Collect$$$$0000000C) refers to main.o(.text) for main
    uldiv.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    uldiv.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    errno.o(i.__aeabi_errno_addr) refers to errno.o(.data) for .data
    errno.o(i.__read_errno) refers to errno.o(.data) for .data
    errno.o(i.__set_errno) refers to errno.o(.data) for .data
    depilogue.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    depilogue.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dfixul.o(.text) refers to llshl.o(.text) for __aeabi_llsl
    init.o(.text) refers to entry5.o(.ARM.Collect$$$$00000004) for __main_after_scatterload
    dsqrt.o(.text) refers to llushr.o(.text) for __aeabi_llsr
    dsqrt.o(.text) refers to depilogue.o(.text) for _double_round


==============================================================================

Removing Unused input sections from the image.

    Removing main.o(.rev16_text), (4 bytes).
    Removing main.o(.revsh_text), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing main.o(.data), (4 bytes).
    Removing stm32f4xx_it.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_it.o(.revsh_text), (4 bytes).
    Removing system_stm32f4xx.o(.rev16_text), (4 bytes).
    Removing system_stm32f4xx.o(.revsh_text), (4 bytes).
    Removing led.o(.rev16_text), (4 bytes).
    Removing led.o(.revsh_text), (4 bytes).
    Removing timer.o(.rev16_text), (4 bytes).
    Removing timer.o(.revsh_text), (4 bytes).
    Removing timer.o(.data), (4 bytes).
    Removing control.o(.rev16_text), (4 bytes).
    Removing control.o(.revsh_text), (4 bytes).
    Removing encoder.o(.rev16_text), (4 bytes).
    Removing encoder.o(.revsh_text), (4 bytes).
    Removing motor.o(.rev16_text), (4 bytes).
    Removing motor.o(.revsh_text), (4 bytes).
    Removing motor.o(.data), (4 bytes).
    Removing motor.o(.data), (4 bytes).
    Removing motor.o(.data), (4 bytes).
    Removing motor.o(.data), (4 bytes).
    Removing motor.o(.data), (4 bytes).
    Removing motor.o(.data), (4 bytes).
    Removing motor.o(.data), (4 bytes).
    Removing motor.o(.data), (4 bytes).
    Removing oled.o(.rev16_text), (4 bytes).
    Removing oled.o(.revsh_text), (4 bytes).
    Removing pwm.o(.rev16_text), (4 bytes).
    Removing pwm.o(.revsh_text), (4 bytes).
    Removing serial.o(.rev16_text), (4 bytes).
    Removing serial.o(.revsh_text), (4 bytes).
    Removing menu.o(.rev16_text), (4 bytes).
    Removing menu.o(.revsh_text), (4 bytes).
    Removing menu.o(.text), (620 bytes).
    Removing key.o(.rev16_text), (4 bytes).
    Removing key.o(.revsh_text), (4 bytes).
    Removing myiic.o(.rev16_text), (4 bytes).
    Removing myiic.o(.revsh_text), (4 bytes).
    Removing myiic.o(.text), (512 bytes).
    Removing mpu6050.o(.rev16_text), (4 bytes).
    Removing mpu6050.o(.revsh_text), (4 bytes).
    Removing mpu6050.o(.text), (788 bytes).
    Removing inv_mpu.o(.rev16_text), (4 bytes).
    Removing inv_mpu.o(.revsh_text), (4 bytes).
    Removing inv_mpu.o(.text), (7568 bytes).
    Removing inv_mpu.o(.constdata), (27 bytes).
    Removing inv_mpu.o(.constdata), (12 bytes).
    Removing inv_mpu.o(.constdata), (40 bytes).
    Removing inv_mpu.o(.conststring), (77 bytes).
    Removing inv_mpu.o(.data), (53 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.rev16_text), (4 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.revsh_text), (4 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.text), (3108 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.bss), (16 bytes).
    Removing inv_mpu_dmp_motion_driver.o(.constdata), (3062 bytes).
    Removing hc_sr04.o(.rev16_text), (4 bytes).
    Removing hc_sr04.o(.revsh_text), (4 bytes).
    Removing tcs34725.o(.rev16_text), (4 bytes).
    Removing tcs34725.o(.revsh_text), (4 bytes).
    Removing tcs34725.o(.text), (1410 bytes).
    Removing tcs34725.o(.data), (8 bytes).
    Removing tcs34725.o(.data), (4 bytes).
    Removing tcrt5000.o(.rev16_text), (4 bytes).
    Removing tcrt5000.o(.revsh_text), (4 bytes).
    Removing tcrt5000.o(.text), (112 bytes).
    Removing tcrt5000.o(.bss), (20 bytes).
    Removing tcrt5000.o(.data), (4 bytes).
    Removing tcrt5000.o(.data), (4 bytes).
    Removing tcrt5000.o(.data), (4 bytes).
    Removing tcrt5000.o(.data), (4 bytes).
    Removing tcrt5000.o(.data), (4 bytes).
    Removing tcrt5000.o(.data), (4 bytes).
    Removing tcrt5000.o(.data), (4 bytes).
    Removing delay.o(.rev16_text), (4 bytes).
    Removing delay.o(.revsh_text), (4 bytes).
    Removing sys.o(.rev16_text), (4 bytes).
    Removing sys.o(.revsh_text), (4 bytes).
    Removing sys.o(.emb_text), (16 bytes).
    Removing usart.o(.rev16_text), (4 bytes).
    Removing usart.o(.revsh_text), (4 bytes).
    Removing usart.o(.text), (188 bytes).
    Removing usart.o(.bss), (200 bytes).
    Removing usart.o(.data), (2 bytes).
    Removing usart.o(.data), (4 bytes).
    Removing startup_stm32f40_41xxx.o(HEAP), (512 bytes).
    Removing misc.o(.rev16_text), (4 bytes).
    Removing misc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_gpio.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_rcc.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_syscfg.o(.text), (120 bytes).
    Removing stm32f4xx_tim.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_tim.o(.revsh_text), (4 bytes).
    Removing stm32f4xx_usart.o(.rev16_text), (4 bytes).
    Removing stm32f4xx_usart.o(.revsh_text), (4 bytes).
    Removing dneg.o(.text), (6 bytes).
    Removing cdcmple.o(.text), (48 bytes).
    Removing dsqrt.o(.text), (162 bytes).

104 unused section(s) (total 19003 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/microlib/division.c              0x00000000   Number         0  ldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uldiv.o ABSOLUTE
    ../clib/microlib/division.c              0x00000000   Number         0  uidiv.o ABSOLUTE
    ../clib/microlib/errno.c                 0x00000000   Number         0  errno.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry8a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry7a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry5.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry11a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry10a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9b.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry2.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry9a.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry.o ABSOLUTE
    ../clib/microlib/init/entry.s            0x00000000   Number         0  entry12a.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llshl.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llushr.o ABSOLUTE
    ../clib/microlib/longlong.c              0x00000000   Number         0  llsshr.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfb.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf7.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf4.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf6.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf0.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf3.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printfa.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf1.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf2.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf5.o ABSOLUTE
    ../clib/microlib/printf/printf.c         0x00000000   Number         0  printf8.o ABSOLUTE
    ../clib/microlib/printf/stubs.s          0x00000000   Number         0  stubs.o ABSOLUTE
    ../clib/microlib/string/memcmp.c         0x00000000   Number         0  memcmp.o ABSOLUTE
    ../clib/microlib/string/memset.c         0x00000000   Number         0  memseta.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  useno.o ABSOLUTE
    ../clib/microlib/stubs.s                 0x00000000   Number         0  iusefp.o ABSOLUTE
    ../fplib/microlib/d2f.c                  0x00000000   Number         0  d2f.o ABSOLUTE
    ../fplib/microlib/f2d.c                  0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/microlib/fpadd.c                0x00000000   Number         0  dadd.o ABSOLUTE
    ../fplib/microlib/fpdiv.c                0x00000000   Number         0  ddiv.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  depilogue.o ABSOLUTE
    ../fplib/microlib/fpepilogue.c           0x00000000   Number         0  fepilogue.o ABSOLUTE
    ../fplib/microlib/fpfix.c                0x00000000   Number         0  dfixul.o ABSOLUTE
    ../fplib/microlib/fpflt.c                0x00000000   Number         0  dflti.o ABSOLUTE
    ../fplib/microlib/fpmul.c                0x00000000   Number         0  dmul.o ABSOLUTE
    ../fplib/microlib/fpneg.c                0x00000000   Number         0  dneg.o ABSOLUTE
    ../fplib/microlib/fpsqrt.c               0x00000000   Number         0  dsqrt.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin.o ABSOLUTE
    ../mathlib/asin.c                        0x00000000   Number         0  asin_x.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan.o ABSOLUTE
    ../mathlib/atan.c                        0x00000000   Number         0  atan_x.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2.o ABSOLUTE
    ../mathlib/atan2.c                       0x00000000   Number         0  atan2_x.o ABSOLUTE
    ../mathlib/dunder.c                      0x00000000   Number         0  dunder.o ABSOLUTE
    ../mathlib/fabs.c                        0x00000000   Number         0  fabs.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ../mathlib/poly.c                        0x00000000   Number         0  poly.o ABSOLUTE
    ../mathlib/qnan.c                        0x00000000   Number         0  qnan.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt.o ABSOLUTE
    ../mathlib/sqrt.c                        0x00000000   Number         0  sqrt_x.o ABSOLUTE
    ..\CORE\startup_stm32f40_41xxx.s         0x00000000   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    ..\FWLIB\src\misc.c                      0x00000000   Number         0  misc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_gpio.c            0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_rcc.c             0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_syscfg.c          0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_tim.c             0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\FWLIB\src\stm32f4xx_usart.c           0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\HARDWARE\HC_SR04\hc_sr04.c            0x00000000   Number         0  hc_sr04.o ABSOLUTE
    ..\HARDWARE\IIC\myiic.c                  0x00000000   Number         0  myiic.o ABSOLUTE
    ..\HARDWARE\KEY\key.c                    0x00000000   Number         0  key.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\HARDWARE\MENU\menu.c                  0x00000000   Number         0  menu.o ABSOLUTE
    ..\HARDWARE\MOTOR\motor.c                0x00000000   Number         0  motor.o ABSOLUTE
    ..\HARDWARE\MPU6050\eMPL\inv_mpu.c       0x00000000   Number         0  inv_mpu.o ABSOLUTE
    ..\HARDWARE\MPU6050\eMPL\inv_mpu_dmp_motion_driver.c 0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    ..\HARDWARE\MPU6050\mpu6050.c            0x00000000   Number         0  mpu6050.o ABSOLUTE
    ..\HARDWARE\OLED\OLED.c                  0x00000000   Number         0  oled.o ABSOLUTE
    ..\HARDWARE\PID\pid.c                    0x00000000   Number         0  pid.o ABSOLUTE
    ..\HARDWARE\PWM\pwm.c                    0x00000000   Number         0  pwm.o ABSOLUTE
    ..\HARDWARE\Serial\Serial.c              0x00000000   Number         0  serial.o ABSOLUTE
    ..\HARDWARE\TCRT5000\TCRT5000.c          0x00000000   Number         0  tcrt5000.o ABSOLUTE
    ..\HARDWARE\TIMER\timer.c                0x00000000   Number         0  timer.o ABSOLUTE
    ..\HARDWARE\control\control.c            0x00000000   Number         0  control.o ABSOLUTE
    ..\HARDWARE\encoder\encoder.c            0x00000000   Number         0  encoder.o ABSOLUTE
    ..\HARDWARE\tcs34725\tcs34725.c          0x00000000   Number         0  tcs34725.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\FWLIB\\src\\misc.c                   0x00000000   Number         0  misc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_gpio.c         0x00000000   Number         0  stm32f4xx_gpio.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_rcc.c          0x00000000   Number         0  stm32f4xx_rcc.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_syscfg.c       0x00000000   Number         0  stm32f4xx_syscfg.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_tim.c          0x00000000   Number         0  stm32f4xx_tim.o ABSOLUTE
    ..\\FWLIB\\src\\stm32f4xx_usart.c        0x00000000   Number         0  stm32f4xx_usart.o ABSOLUTE
    ..\\HARDWARE\\HC_SR04\\hc_sr04.c         0x00000000   Number         0  hc_sr04.o ABSOLUTE
    ..\\HARDWARE\\IIC\\myiic.c               0x00000000   Number         0  myiic.o ABSOLUTE
    ..\\HARDWARE\\KEY\\key.c                 0x00000000   Number         0  key.o ABSOLUTE
    ..\\HARDWARE\\LED\\led.c                 0x00000000   Number         0  led.o ABSOLUTE
    ..\\HARDWARE\\MENU\\menu.c               0x00000000   Number         0  menu.o ABSOLUTE
    ..\\HARDWARE\\MOTOR\\motor.c             0x00000000   Number         0  motor.o ABSOLUTE
    ..\\HARDWARE\\MPU6050\\eMPL\\inv_mpu.c   0x00000000   Number         0  inv_mpu.o ABSOLUTE
    ..\\HARDWARE\\MPU6050\\eMPL\\inv_mpu_dmp_motion_driver.c 0x00000000   Number         0  inv_mpu_dmp_motion_driver.o ABSOLUTE
    ..\\HARDWARE\\MPU6050\\mpu6050.c         0x00000000   Number         0  mpu6050.o ABSOLUTE
    ..\\HARDWARE\\OLED\\OLED.c               0x00000000   Number         0  oled.o ABSOLUTE
    ..\\HARDWARE\\PWM\\pwm.c                 0x00000000   Number         0  pwm.o ABSOLUTE
    ..\\HARDWARE\\Serial\\Serial.c           0x00000000   Number         0  serial.o ABSOLUTE
    ..\\HARDWARE\\TCRT5000\\TCRT5000.c       0x00000000   Number         0  tcrt5000.o ABSOLUTE
    ..\\HARDWARE\\TIMER\\timer.c             0x00000000   Number         0  timer.o ABSOLUTE
    ..\\HARDWARE\\control\\control.c         0x00000000   Number         0  control.o ABSOLUTE
    ..\\HARDWARE\\encoder\\encoder.c         0x00000000   Number         0  encoder.o ABSOLUTE
    ..\\HARDWARE\\tcs34725\\tcs34725.c       0x00000000   Number         0  tcs34725.o ABSOLUTE
    ..\\SYSTEM\\delay\\delay.c               0x00000000   Number         0  delay.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    ..\\SYSTEM\\usart\\usart.c               0x00000000   Number         0  usart.o ABSOLUTE
    cdcmple.s                                0x00000000   Number         0  cdcmple.o ABSOLUTE
    cdrcmple.s                               0x00000000   Number         0  cdrcmple.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    handlers.s                               0x00000000   Number         0  handlers.o ABSOLUTE
    init.s                                   0x00000000   Number         0  init.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    stm32f4xx_it.c                           0x00000000   Number         0  stm32f4xx_it.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    system_stm32f4xx.c                       0x00000000   Number         0  system_stm32f4xx.o ABSOLUTE
    RESET                                    0x08000000   Section      392  startup_stm32f40_41xxx.o(RESET)
    .ARM.Collect$$$$00000000                 0x08000188   Section        0  entry.o(.ARM.Collect$$$$00000000)
    .ARM.Collect$$$$00000001                 0x08000188   Section        4  entry2.o(.ARM.Collect$$$$00000001)
    .ARM.Collect$$$$00000004                 0x0800018c   Section        4  entry5.o(.ARM.Collect$$$$00000004)
    .ARM.Collect$$$$00000008                 0x08000190   Section        0  entry7b.o(.ARM.Collect$$$$00000008)
    .ARM.Collect$$$$0000000A                 0x08000190   Section        0  entry8b.o(.ARM.Collect$$$$0000000A)
    .ARM.Collect$$$$0000000B                 0x08000190   Section        8  entry9a.o(.ARM.Collect$$$$0000000B)
    .ARM.Collect$$$$0000000E                 0x08000198   Section        4  entry12b.o(.ARM.Collect$$$$0000000E)
    .ARM.Collect$$$$0000000F                 0x0800019c   Section        0  entry10a.o(.ARM.Collect$$$$0000000F)
    .ARM.Collect$$$$00000011                 0x0800019c   Section        0  entry11a.o(.ARM.Collect$$$$00000011)
    .ARM.Collect$$$$00002712                 0x0800019c   Section        4  entry2.o(.ARM.Collect$$$$00002712)
    __lit__00000000                          0x0800019c   Data           4  entry2.o(.ARM.Collect$$$$00002712)
    .text                                    0x080001a0   Section        0  main.o(.text)
    .text                                    0x080003b8   Section        0  stm32f4xx_it.o(.text)
    .text                                    0x080003cc   Section        0  system_stm32f4xx.o(.text)
    SetSysClock                              0x080003cd   Thumb Code   168  system_stm32f4xx.o(.text)
    .text                                    0x0800055c   Section        0  led.o(.text)
    .text                                    0x080005e4   Section        0  timer.o(.text)
    .text                                    0x0800085c   Section        0  control.o(.text)
    .text                                    0x080008bc   Section        0  encoder.o(.text)
    .text                                    0x08000afc   Section        0  motor.o(.text)
    .text                                    0x08000cac   Section        0  oled.o(.text)
    .text                                    0x08001114   Section        0  pwm.o(.text)
    .text                                    0x08001324   Section        0  serial.o(.text)
    .text                                    0x080017b0   Section        0  pid.o(.text)
    .text                                    0x080019a8   Section        0  key.o(.text)
    .text                                    0x08001a98   Section        0  hc_sr04.o(.text)
    .text                                    0x08001c1c   Section        0  delay.o(.text)
    .text                                    0x08001cd4   Section       36  startup_stm32f40_41xxx.o(.text)
    $v0                                      0x08001cd4   Number         0  startup_stm32f40_41xxx.o(.text)
    .text                                    0x08001cf8   Section        0  misc.o(.text)
    .text                                    0x08001db4   Section        0  stm32f4xx_gpio.o(.text)
    .text                                    0x08002018   Section        0  stm32f4xx_rcc.o(.text)
    .text                                    0x080024f8   Section        0  stm32f4xx_tim.o(.text)
    TI4_Config                               0x08002ba5   Thumb Code    54  stm32f4xx_tim.o(.text)
    TI3_Config                               0x08002beb   Thumb Code    50  stm32f4xx_tim.o(.text)
    TI2_Config                               0x08002c35   Thumb Code    54  stm32f4xx_tim.o(.text)
    TI1_Config                               0x08002c7b   Thumb Code    46  stm32f4xx_tim.o(.text)
    .text                                    0x08002fbc   Section        0  stm32f4xx_usart.o(.text)
    .text                                    0x080033b0   Section        0  dadd.o(.text)
    .text                                    0x080034fe   Section        0  dmul.o(.text)
    .text                                    0x080035e2   Section        0  ddiv.o(.text)
    .text                                    0x080036c0   Section        0  dflti.o(.text)
    .text                                    0x080036e2   Section        0  f2d.o(.text)
    .text                                    0x08003708   Section        0  d2f.o(.text)
    .text                                    0x08003740   Section        0  uidiv.o(.text)
    .text                                    0x0800376c   Section        0  uldiv.o(.text)
    .text                                    0x080037ce   Section        0  llshl.o(.text)
    .text                                    0x080037ec   Section        0  llsshr.o(.text)
    .text                                    0x08003810   Section        0  iusefp.o(.text)
    .text                                    0x08003810   Section        0  fepilogue.o(.text)
    .text                                    0x0800387e   Section        0  depilogue.o(.text)
    .text                                    0x08003938   Section        0  dfixul.o(.text)
    .text                                    0x08003968   Section       48  cdrcmple.o(.text)
    .text                                    0x08003998   Section       36  init.o(.text)
    .text                                    0x080039bc   Section        0  llushr.o(.text)
    i.__0sprintf                             0x080039dc   Section        0  printfa.o(i.__0sprintf)
    i.__scatterload_copy                     0x08003a04   Section       14  handlers.o(i.__scatterload_copy)
    i.__scatterload_null                     0x08003a12   Section        2  handlers.o(i.__scatterload_null)
    i.__scatterload_zeroinit                 0x08003a14   Section       14  handlers.o(i.__scatterload_zeroinit)
    i._fp_digits                             0x08003a24   Section        0  printfa.o(i._fp_digits)
    _fp_digits                               0x08003a25   Thumb Code   366  printfa.o(i._fp_digits)
    i._printf_core                           0x08003ba8   Section        0  printfa.o(i._printf_core)
    _printf_core                             0x08003ba9   Thumb Code  1704  printfa.o(i._printf_core)
    i._printf_post_padding                   0x0800425c   Section        0  printfa.o(i._printf_post_padding)
    _printf_post_padding                     0x0800425d   Thumb Code    36  printfa.o(i._printf_post_padding)
    i._printf_pre_padding                    0x08004280   Section        0  printfa.o(i._printf_pre_padding)
    _printf_pre_padding                      0x08004281   Thumb Code    46  printfa.o(i._printf_pre_padding)
    i._sputc                                 0x080042ae   Section        0  printfa.o(i._sputc)
    _sputc                                   0x080042af   Thumb Code    10  printfa.o(i._sputc)
    .constdata                               0x080042b8   Section     1520  oled.o(.constdata)
    .data                                    0x20000000   Section        8  main.o(.data)
    .data                                    0x20000008   Section       20  system_stm32f4xx.o(.data)
    .data                                    0x2000001c   Section        4  led.o(.data)
    .data                                    0x20000020   Section       16  timer.o(.data)
    count                                    0x20000028   Data           4  timer.o(.data)
    .data                                    0x20000030   Section       16  encoder.o(.data)
    .data                                    0x20000040   Section       36  serial.o(.data)
    stored_value                             0x20000040   Data           1  serial.o(.data)
    i                                        0x20000050   Data           4  serial.o(.data)
    .data                                    0x20000064   Section        8  pid.o(.data)
    .data                                    0x2000006c   Section        8  key.o(.data)
    key_up                                   0x2000006c   Data           1  key.o(.data)
    .data                                    0x20000074   Section        4  hc_sr04.o(.data)
    .data                                    0x20000078   Section        4  delay.o(.data)
    fac_us                                   0x20000078   Data           1  delay.o(.data)
    fac_ms                                   0x2000007a   Data           2  delay.o(.data)
    .data                                    0x2000007c   Section       16  stm32f4xx_rcc.o(.data)
    APBAHBPrescTable                         0x2000007c   Data          16  stm32f4xx_rcc.o(.data)
    .bss                                     0x2000008c   Section      100  main.o(.bss)
    .bss                                     0x200000f0   Section       15  serial.o(.bss)
    .bss                                     0x20000100   Section      128  pid.o(.bss)
    STACK                                    0x20000180   Section     1024  startup_stm32f40_41xxx.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$E$P$D$K$B$S$7EM$VFPi3$EXTD16$VFPS$VFMA$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEX$EBA8$MICROLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    __use_no_errno                           0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_exception_handling              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_fp                              0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap                            0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_heap_region                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting                     0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_semihosting_swi                 0x00000000   Number         0  useno.o ABSOLUTE
    __use_no_signal_handling                 0x00000000   Number         0  useno.o ABSOLUTE
    _printf_a                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_c                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_charcount                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_d                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_e                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_f                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_dec                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_fp_hex                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_g                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_i                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_int_dec                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_l                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ll                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lld                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_lli                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llo                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llu                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_llx                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_dec                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_hex                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_longlong_oct                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_ls                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_mbtowc                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_n                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_o                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_p                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_percent                          0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_pre_padding                      0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_s                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_str                              0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_signed                  0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_truncate_unsigned                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_u                                0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wc                               0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_wctomb                           0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  stubs.o ABSOLUTE
    _printf_x                                0x00000000   Number         0  stubs.o ABSOLUTE
    __arm_fini_                               - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __decompress                              - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _microlib_exit                            - Undefined Weak Reference
    __Vectors_Size                           0x00000188   Number         0  startup_stm32f40_41xxx.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f40_41xxx.o(RESET)
    __Vectors_End                            0x08000188   Data           0  startup_stm32f40_41xxx.o(RESET)
    __main                                   0x08000189   Thumb Code     0  entry.o(.ARM.Collect$$$$00000000)
    _main_stk                                0x08000189   Thumb Code     0  entry2.o(.ARM.Collect$$$$00000001)
    _main_scatterload                        0x0800018d   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    __main_after_scatterload                 0x08000191   Thumb Code     0  entry5.o(.ARM.Collect$$$$00000004)
    _main_clock                              0x08000191   Thumb Code     0  entry7b.o(.ARM.Collect$$$$00000008)
    _main_cpp_init                           0x08000191   Thumb Code     0  entry8b.o(.ARM.Collect$$$$0000000A)
    _main_init                               0x08000191   Thumb Code     0  entry9a.o(.ARM.Collect$$$$0000000B)
    __rt_lib_shutdown_fini                   0x08000199   Thumb Code     0  entry12b.o(.ARM.Collect$$$$0000000E)
    __rt_final_cpp                           0x0800019d   Thumb Code     0  entry10a.o(.ARM.Collect$$$$0000000F)
    __rt_final_exit                          0x0800019d   Thumb Code     0  entry11a.o(.ARM.Collect$$$$00000011)
    main                                     0x080001a1   Thumb Code   476  main.o(.text)
    NMI_Handler                              0x080003b9   Thumb Code     2  stm32f4xx_it.o(.text)
    HardFault_Handler                        0x080003bb   Thumb Code     2  stm32f4xx_it.o(.text)
    MemManage_Handler                        0x080003bd   Thumb Code     2  stm32f4xx_it.o(.text)
    BusFault_Handler                         0x080003bf   Thumb Code     2  stm32f4xx_it.o(.text)
    UsageFault_Handler                       0x080003c1   Thumb Code     2  stm32f4xx_it.o(.text)
    SVC_Handler                              0x080003c3   Thumb Code     2  stm32f4xx_it.o(.text)
    DebugMon_Handler                         0x080003c5   Thumb Code     2  stm32f4xx_it.o(.text)
    PendSV_Handler                           0x080003c7   Thumb Code     2  stm32f4xx_it.o(.text)
    SysTick_Handler                          0x080003c9   Thumb Code     2  stm32f4xx_it.o(.text)
    SystemInit                               0x08000475   Thumb Code    74  system_stm32f4xx.o(.text)
    SystemCoreClockUpdate                    0x080004bf   Thumb Code   122  system_stm32f4xx.o(.text)
    LED_Init                                 0x0800055d   Thumb Code    58  led.o(.text)
    hw_oled                                  0x08000597   Thumb Code    56  led.o(.text)
    TIM3_Int_Init                            0x080005e5   Thumb Code    88  timer.o(.text)
    TIM3_IRQHandler                          0x0800063d   Thumb Code   504  timer.o(.text)
    Control_Init                             0x0800085d   Thumb Code    86  control.o(.text)
    Encoder_Init_TIM4                        0x080008bd   Thumb Code   190  encoder.o(.text)
    Encoder_Init_TIM2                        0x0800097b   Thumb Code   186  encoder.o(.text)
    read_Speed1                              0x08000a35   Thumb Code    86  encoder.o(.text)
    read_Speed2                              0x08000a8b   Thumb Code    86  encoder.o(.text)
    MotorB_Set                               0x08000afd   Thumb Code   162  motor.o(.text)
    motorPidSetSpeed                         0x08000b9f   Thumb Code    62  motor.o(.text)
    Car_Run                                  0x08000bdd   Thumb Code   168  motor.o(.text)
    OLED_I2C_Init                            0x08000cad   Thumb Code    68  oled.o(.text)
    OLED_I2C_Start                           0x08000cf1   Thumb Code    72  oled.o(.text)
    OLED_I2C_Stop                            0x08000d39   Thumb Code    56  oled.o(.text)
    OLED_I2C_SendByte                        0x08000d71   Thumb Code   108  oled.o(.text)
    OLED_WriteCommand                        0x08000ddd   Thumb Code    32  oled.o(.text)
    OLED_WriteData                           0x08000dfd   Thumb Code    32  oled.o(.text)
    OLED_SetCursor                           0x08000e1d   Thumb Code    32  oled.o(.text)
    OLED_Set_Pos                             0x08000e3d   Thumb Code    36  oled.o(.text)
    OLED_Clear                               0x08000e61   Thumb Code    38  oled.o(.text)
    OLED_ShowChar                            0x08000e87   Thumb Code    82  oled.o(.text)
    Draw_BMP                                 0x08000ed9   Thumb Code    68  oled.o(.text)
    OLED_ShowString                          0x08000f1d   Thumb Code    36  oled.o(.text)
    OLED_Pow                                 0x08000f41   Thumb Code    16  oled.o(.text)
    OLED_ShowNum                             0x08000f51   Thumb Code    62  oled.o(.text)
    OLED_ShowSignedNum                       0x08000f8f   Thumb Code    96  oled.o(.text)
    OLED_ShowHexNum                          0x08000fef   Thumb Code    78  oled.o(.text)
    OLED_ShowBinNum                          0x0800103d   Thumb Code    56  oled.o(.text)
    OLED_Init                                0x08001075   Thumb Code   160  oled.o(.text)
    TIM1_PWM_Init                            0x08001115   Thumb Code   228  pwm.o(.text)
    TIM9_PWM_Init                            0x080011f9   Thumb Code   236  pwm.o(.text)
    Servo_Set                                0x080012e5   Thumb Code    36  pwm.o(.text)
    Serial_Init                              0x08001325   Thumb Code   172  serial.o(.text)
    Serial3_Init                             0x080013d1   Thumb Code   170  serial.o(.text)
    Serial2_Init                             0x0800147b   Thumb Code   168  serial.o(.text)
    Serial4_Init                             0x08001523   Thumb Code   170  serial.o(.text)
    Serial_SendByte                          0x080015cd   Thumb Code    26  serial.o(.text)
    Serial_SendArray                         0x080015e7   Thumb Code    26  serial.o(.text)
    Serial_SendString                        0x08001601   Thumb Code    24  serial.o(.text)
    Serial_Pow                               0x08001619   Thumb Code    16  serial.o(.text)
    Serial_SendNumber                        0x08001629   Thumb Code    54  serial.o(.text)
    USART1_IRQHandler                        0x0800165f   Thumb Code   124  serial.o(.text)
    USART3_IRQHandler                        0x080016db   Thumb Code    50  serial.o(.text)
    USART2_IRQHandler                        0x0800170d   Thumb Code    90  serial.o(.text)
    UART4_IRQHandler                         0x08001767   Thumb Code    50  serial.o(.text)
    num_deal                                 0x08001799   Thumb Code    14  serial.o(.text)
    PID_init                                 0x080017b1   Thumb Code   166  pid.o(.text)
    P_realize                                0x08001857   Thumb Code    26  pid.o(.text)
    PI_realize                               0x08001871   Thumb Code    46  pid.o(.text)
    PID_realize                              0x0800189f   Thumb Code    96  pid.o(.text)
    PD_realize                               0x080018ff   Thumb Code   118  pid.o(.text)
    KEY_Init                                 0x080019a9   Thumb Code    42  key.o(.text)
    KEY_Scan                                 0x080019d3   Thumb Code   190  key.o(.text)
    TIM5_Int_Init                            0x08001a99   Thumb Code   154  hc_sr04.o(.text)
    Senor_Using                              0x08001b33   Thumb Code   172  hc_sr04.o(.text)
    TIM5_IRQHandler                          0x08001bdf   Thumb Code    34  hc_sr04.o(.text)
    delay_init                               0x08001c1d   Thumb Code    38  delay.o(.text)
    delay_us                                 0x08001c43   Thumb Code    46  delay.o(.text)
    delay_xms                                0x08001c71   Thumb Code    46  delay.o(.text)
    delay_ms                                 0x08001c9f   Thumb Code    50  delay.o(.text)
    Reset_Handler                            0x08001cd5   Thumb Code     8  startup_stm32f40_41xxx.o(.text)
    ADC_IRQHandler                           0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX0_IRQHandler                      0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_RX1_IRQHandler                      0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_SCE_IRQHandler                      0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN1_TX_IRQHandler                       0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX0_IRQHandler                      0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_RX1_IRQHandler                      0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_SCE_IRQHandler                      0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CAN2_TX_IRQHandler                       0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    CRYP_IRQHandler                          0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DCMI_IRQHandler                          0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream0_IRQHandler                  0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream1_IRQHandler                  0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream2_IRQHandler                  0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream3_IRQHandler                  0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream4_IRQHandler                  0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream5_IRQHandler                  0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream6_IRQHandler                  0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA1_Stream7_IRQHandler                  0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream0_IRQHandler                  0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream1_IRQHandler                  0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream2_IRQHandler                  0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream3_IRQHandler                  0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream4_IRQHandler                  0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream5_IRQHandler                  0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream6_IRQHandler                  0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    DMA2_Stream7_IRQHandler                  0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_IRQHandler                           0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    ETH_WKUP_IRQHandler                      0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI0_IRQHandler                         0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI15_10_IRQHandler                     0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI1_IRQHandler                         0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI2_IRQHandler                         0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI3_IRQHandler                         0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI4_IRQHandler                         0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    EXTI9_5_IRQHandler                       0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FLASH_IRQHandler                         0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FPU_IRQHandler                           0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    FSMC_IRQHandler                          0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    HASH_RNG_IRQHandler                      0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_ER_IRQHandler                       0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C1_EV_IRQHandler                       0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_ER_IRQHandler                       0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C2_EV_IRQHandler                       0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_ER_IRQHandler                       0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    I2C3_EV_IRQHandler                       0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_IRQHandler                        0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_FS_WKUP_IRQHandler                   0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_IN_IRQHandler                 0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_EP1_OUT_IRQHandler                0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_IRQHandler                        0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    OTG_HS_WKUP_IRQHandler                   0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    PVD_IRQHandler                           0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RCC_IRQHandler                           0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_Alarm_IRQHandler                     0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    RTC_WKUP_IRQHandler                      0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SDIO_IRQHandler                          0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI1_IRQHandler                          0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI2_IRQHandler                          0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    SPI3_IRQHandler                          0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TAMP_STAMP_IRQHandler                    0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_BRK_TIM9_IRQHandler                 0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_CC_IRQHandler                       0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_TRG_COM_TIM11_IRQHandler            0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM1_UP_TIM10_IRQHandler                 0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM2_IRQHandler                          0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM4_IRQHandler                          0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM6_DAC_IRQHandler                      0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM7_IRQHandler                          0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_BRK_TIM12_IRQHandler                0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_CC_IRQHandler                       0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_TRG_COM_TIM14_IRQHandler            0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    TIM8_UP_TIM13_IRQHandler                 0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    UART5_IRQHandler                         0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    USART6_IRQHandler                        0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    WWDG_IRQHandler                          0x08001cef   Thumb Code     0  startup_stm32f40_41xxx.o(.text)
    NVIC_PriorityGroupConfig                 0x08001cf9   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x08001d03   Thumb Code    98  misc.o(.text)
    NVIC_SetVectorTable                      0x08001d65   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x08001d73   Thumb Code    24  misc.o(.text)
    SysTick_CLKSourceConfig                  0x08001d8b   Thumb Code    28  misc.o(.text)
    GPIO_DeInit                              0x08001db5   Thumb Code   296  stm32f4xx_gpio.o(.text)
    GPIO_Init                                0x08001edd   Thumb Code   124  stm32f4xx_gpio.o(.text)
    GPIO_StructInit                          0x08001f59   Thumb Code    18  stm32f4xx_gpio.o(.text)
    GPIO_PinLockConfig                       0x08001f6b   Thumb Code    26  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x08001f85   Thumb Code    14  stm32f4xx_gpio.o(.text)
    GPIO_ReadInputData                       0x08001f93   Thumb Code     6  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x08001f99   Thumb Code    14  stm32f4xx_gpio.o(.text)
    GPIO_ReadOutputData                      0x08001fa7   Thumb Code     6  stm32f4xx_gpio.o(.text)
    GPIO_SetBits                             0x08001fad   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ResetBits                           0x08001fb1   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_WriteBit                            0x08001fb5   Thumb Code    12  stm32f4xx_gpio.o(.text)
    GPIO_Write                               0x08001fc1   Thumb Code     4  stm32f4xx_gpio.o(.text)
    GPIO_ToggleBits                          0x08001fc5   Thumb Code     8  stm32f4xx_gpio.o(.text)
    GPIO_PinAFConfig                         0x08001fcd   Thumb Code    32  stm32f4xx_gpio.o(.text)
    RCC_DeInit                               0x08002019   Thumb Code    68  stm32f4xx_rcc.o(.text)
    RCC_HSEConfig                            0x0800205d   Thumb Code    12  stm32f4xx_rcc.o(.text)
    RCC_GetFlagStatus                        0x08002069   Thumb Code    50  stm32f4xx_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x0800209b   Thumb Code    48  stm32f4xx_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x080020cb   Thumb Code    16  stm32f4xx_rcc.o(.text)
    RCC_HSICmd                               0x080020db   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_LSEConfig                            0x080020e1   Thumb Code    30  stm32f4xx_rcc.o(.text)
    RCC_LSICmd                               0x080020ff   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLConfig                            0x08002105   Thumb Code    34  stm32f4xx_rcc.o(.text)
    RCC_PLLCmd                               0x08002127   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SConfig                         0x0800212d   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_PLLI2SCmd                            0x0800213b   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_PLLSAIConfig                         0x08002141   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_PLLSAICmd                            0x08002153   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x08002159   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_MCO1Config                           0x0800215f   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_MCO2Config                           0x08002171   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_SYSCLKConfig                         0x08002183   Thumb Code    16  stm32f4xx_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x08002193   Thumb Code    12  stm32f4xx_rcc.o(.text)
    RCC_HCLKConfig                           0x0800219f   Thumb Code    16  stm32f4xx_rcc.o(.text)
    RCC_PCLK1Config                          0x080021af   Thumb Code    16  stm32f4xx_rcc.o(.text)
    RCC_PCLK2Config                          0x080021bf   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_GetClocksFreq                        0x080021d1   Thumb Code   150  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKConfig                         0x08002267   Thumb Code    42  stm32f4xx_rcc.o(.text)
    RCC_RTCCLKCmd                            0x08002291   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_BackupResetCmd                       0x08002299   Thumb Code     8  stm32f4xx_rcc.o(.text)
    RCC_I2SCLKConfig                         0x080022a1   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLI2SClkDivConfig                0x080022a7   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_SAIPLLSAIClkDivConfig                0x080022b9   Thumb Code    20  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockACLKConfig                   0x080022cd   Thumb Code    16  stm32f4xx_rcc.o(.text)
    RCC_SAIBlockBCLKConfig                   0x080022dd   Thumb Code    16  stm32f4xx_rcc.o(.text)
    RCC_LTDCCLKDivConfig                     0x080022ed   Thumb Code    16  stm32f4xx_rcc.o(.text)
    RCC_TIMCLKPresConfig                     0x080022fd   Thumb Code     6  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockCmd                   0x08002303   Thumb Code    24  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockCmd                   0x0800231b   Thumb Code    24  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockCmd                   0x08002333   Thumb Code    24  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x0800234b   Thumb Code    24  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x08002363   Thumb Code    24  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphResetCmd                   0x0800237b   Thumb Code    24  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphResetCmd                   0x08002393   Thumb Code    24  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphResetCmd                   0x080023ab   Thumb Code    24  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x080023c3   Thumb Code    24  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x080023db   Thumb Code    24  stm32f4xx_rcc.o(.text)
    RCC_AHB1PeriphClockLPModeCmd             0x080023f3   Thumb Code    70  stm32f4xx_rcc.o(.text)
    RCC_AHB2PeriphClockLPModeCmd             0x08002439   Thumb Code    22  stm32f4xx_rcc.o(.text)
    RCC_AHB3PeriphClockLPModeCmd             0x0800244f   Thumb Code    24  stm32f4xx_rcc.o(.text)
    RCC_APB1PeriphClockLPModeCmd             0x08002467   Thumb Code    24  stm32f4xx_rcc.o(.text)
    RCC_APB2PeriphClockLPModeCmd             0x0800247f   Thumb Code    24  stm32f4xx_rcc.o(.text)
    RCC_LSEModeConfig                        0x08002497   Thumb Code    28  stm32f4xx_rcc.o(.text)
    RCC_ITConfig                             0x080024b3   Thumb Code    24  stm32f4xx_rcc.o(.text)
    RCC_ClearFlag                            0x080024cb   Thumb Code    14  stm32f4xx_rcc.o(.text)
    RCC_GetITStatus                          0x080024d9   Thumb Code    18  stm32f4xx_rcc.o(.text)
    RCC_ClearITPendingBit                    0x080024eb   Thumb Code     8  stm32f4xx_rcc.o(.text)
    TIM_DeInit                               0x080024f9   Thumb Code   376  stm32f4xx_tim.o(.text)
    TIM_TimeBaseInit                         0x08002671   Thumb Code    96  stm32f4xx_tim.o(.text)
    TIM_TimeBaseStructInit                   0x080026d1   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_PrescalerConfig                      0x080026e3   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_CounterModeConfig                    0x080026e9   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_SetCounter                           0x080026f5   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetAutoreload                        0x080026f9   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetCounter                           0x080026fd   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetPrescaler                         0x08002701   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_UpdateDisableConfig                  0x08002705   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_UpdateRequestConfig                  0x0800271d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ARRPreloadConfig                     0x08002735   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_SelectOnePulseMode                   0x0800274d   Thumb Code    16  stm32f4xx_tim.o(.text)
    TIM_SetClockDivision                     0x0800275d   Thumb Code    16  stm32f4xx_tim.o(.text)
    TIM_Cmd                                  0x0800276d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_OC1Init                              0x08002785   Thumb Code    96  stm32f4xx_tim.o(.text)
    TIM_OC2Init                              0x080027e5   Thumb Code   128  stm32f4xx_tim.o(.text)
    TIM_OC3Init                              0x08002865   Thumb Code   176  stm32f4xx_tim.o(.text)
    TIM_OC4Init                              0x08002915   Thumb Code    92  stm32f4xx_tim.o(.text)
    TIM_OCStructInit                         0x08002971   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_SelectOCxM                           0x08002985   Thumb Code    80  stm32f4xx_tim.o(.text)
    TIM_SetCompare1                          0x080029d5   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare2                          0x080029d9   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare3                          0x080029dd   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_SetCompare4                          0x080029e1   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_ForcedOC1Config                      0x080029e5   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_ForcedOC2Config                      0x080029f1   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_ForcedOC3Config                      0x08002a05   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_ForcedOC4Config                      0x08002a11   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_OC1PreloadConfig                     0x08002a25   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_OC2PreloadConfig                     0x08002a31   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_OC3PreloadConfig                     0x08002a45   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_OC4PreloadConfig                     0x08002a51   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_OC1FastConfig                        0x08002a65   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_OC2FastConfig                        0x08002a71   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_OC3FastConfig                        0x08002a85   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_OC4FastConfig                        0x08002a91   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_ClearOC1Ref                          0x08002aa5   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_ClearOC2Ref                          0x08002ab1   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_ClearOC3Ref                          0x08002ac5   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_ClearOC4Ref                          0x08002ad1   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_OC1PolarityConfig                    0x08002ae5   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_OC1NPolarityConfig                   0x08002af1   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_OC2PolarityConfig                    0x08002afd   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_OC2NPolarityConfig                   0x08002b11   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_OC3PolarityConfig                    0x08002b25   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_OC3NPolarityConfig                   0x08002b39   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_OC4PolarityConfig                    0x08002b4d   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_CCxCmd                               0x08002b61   Thumb Code    22  stm32f4xx_tim.o(.text)
    TIM_CCxNCmd                              0x08002b77   Thumb Code    22  stm32f4xx_tim.o(.text)
    TIM_SetIC4Prescaler                      0x08002b8d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_SetIC3Prescaler                      0x08002bdb   Thumb Code    16  stm32f4xx_tim.o(.text)
    TIM_SetIC2Prescaler                      0x08002c1d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_SetIC1Prescaler                      0x08002c6b   Thumb Code    16  stm32f4xx_tim.o(.text)
    TIM_ICInit                               0x08002ca9   Thumb Code    90  stm32f4xx_tim.o(.text)
    TIM_ICStructInit                         0x08002d03   Thumb Code    16  stm32f4xx_tim.o(.text)
    TIM_PWMIConfig                           0x08002d13   Thumb Code   116  stm32f4xx_tim.o(.text)
    TIM_GetCapture1                          0x08002d87   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetCapture2                          0x08002d8b   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetCapture3                          0x08002d8f   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetCapture4                          0x08002d93   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_BDTRConfig                           0x08002d97   Thumb Code    34  stm32f4xx_tim.o(.text)
    TIM_BDTRStructInit                       0x08002db9   Thumb Code    18  stm32f4xx_tim.o(.text)
    TIM_CtrlPWMOutputs                       0x08002dcb   Thumb Code    28  stm32f4xx_tim.o(.text)
    TIM_SelectCOM                            0x08002de7   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_CCPreloadControl                     0x08002dff   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ITConfig                             0x08002e17   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_GenerateEvent                        0x08002e2b   Thumb Code     4  stm32f4xx_tim.o(.text)
    TIM_GetFlagStatus                        0x08002e2f   Thumb Code    14  stm32f4xx_tim.o(.text)
    TIM_ClearFlag                            0x08002e3d   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_GetITStatus                          0x08002e43   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ClearITPendingBit                    0x08002e5b   Thumb Code     6  stm32f4xx_tim.o(.text)
    TIM_DMAConfig                            0x08002e61   Thumb Code     8  stm32f4xx_tim.o(.text)
    TIM_DMACmd                               0x08002e69   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_SelectCCDMA                          0x08002e7d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_InternalClockConfig                  0x08002e95   Thumb Code    10  stm32f4xx_tim.o(.text)
    TIM_SelectInputTrigger                   0x08002e9f   Thumb Code    12  stm32f4xx_tim.o(.text)
    TIM_ITRxExternalClockConfig              0x08002eab   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_TIxExternalClockConfig               0x08002ebf   Thumb Code    48  stm32f4xx_tim.o(.text)
    TIM_ETRConfig                            0x08002eef   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode1Config                  0x08002f07   Thumb Code    32  stm32f4xx_tim.o(.text)
    TIM_ETRClockMode2Config                  0x08002f27   Thumb Code    20  stm32f4xx_tim.o(.text)
    TIM_SelectOutputTrigger                  0x08002f3b   Thumb Code    16  stm32f4xx_tim.o(.text)
    TIM_SelectSlaveMode                      0x08002f4b   Thumb Code    16  stm32f4xx_tim.o(.text)
    TIM_SelectMasterSlaveMode                0x08002f5b   Thumb Code    16  stm32f4xx_tim.o(.text)
    TIM_EncoderInterfaceConfig               0x08002f6b   Thumb Code    50  stm32f4xx_tim.o(.text)
    TIM_SelectHallSensor                     0x08002f9d   Thumb Code    24  stm32f4xx_tim.o(.text)
    TIM_RemapConfig                          0x08002fb5   Thumb Code     6  stm32f4xx_tim.o(.text)
    USART_DeInit                             0x08002fbd   Thumb Code   224  stm32f4xx_usart.o(.text)
    USART_Init                               0x0800309d   Thumb Code   180  stm32f4xx_usart.o(.text)
    USART_StructInit                         0x08003151   Thumb Code    22  stm32f4xx_usart.o(.text)
    USART_ClockInit                          0x08003167   Thumb Code    28  stm32f4xx_usart.o(.text)
    USART_ClockStructInit                    0x08003183   Thumb Code    12  stm32f4xx_usart.o(.text)
    USART_Cmd                                0x0800318f   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetPrescaler                       0x080031a7   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_OverSampling8Cmd                   0x080031b7   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_OneBitMethodCmd                    0x080031cf   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendData                           0x080031e7   Thumb Code     8  stm32f4xx_usart.o(.text)
    USART_ReceiveData                        0x080031ef   Thumb Code     8  stm32f4xx_usart.o(.text)
    USART_SetAddress                         0x080031f7   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x08003207   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_WakeUpConfig                       0x0800321f   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x0800322f   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_LINCmd                             0x0800323f   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SendBreak                          0x08003257   Thumb Code    10  stm32f4xx_usart.o(.text)
    USART_HalfDuplexCmd                      0x08003261   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SetGuardTime                       0x08003279   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_SmartCardCmd                       0x08003289   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_SmartCardNACKCmd                   0x080032a1   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_IrDAConfig                         0x080032b9   Thumb Code    16  stm32f4xx_usart.o(.text)
    USART_IrDACmd                            0x080032c9   Thumb Code    24  stm32f4xx_usart.o(.text)
    USART_DMACmd                             0x080032e1   Thumb Code    20  stm32f4xx_usart.o(.text)
    USART_ITConfig                           0x080032f5   Thumb Code    54  stm32f4xx_usart.o(.text)
    USART_GetFlagStatus                      0x0800332b   Thumb Code    14  stm32f4xx_usart.o(.text)
    USART_ClearFlag                          0x08003339   Thumb Code     8  stm32f4xx_usart.o(.text)
    USART_GetITStatus                        0x08003341   Thumb Code    64  stm32f4xx_usart.o(.text)
    USART_ClearITPendingBit                  0x08003381   Thumb Code    16  stm32f4xx_usart.o(.text)
    __aeabi_dadd                             0x080033b1   Thumb Code   322  dadd.o(.text)
    __aeabi_dsub                             0x080034f3   Thumb Code     6  dadd.o(.text)
    __aeabi_drsub                            0x080034f9   Thumb Code     6  dadd.o(.text)
    __aeabi_dmul                             0x080034ff   Thumb Code   228  dmul.o(.text)
    __aeabi_ddiv                             0x080035e3   Thumb Code   222  ddiv.o(.text)
    __aeabi_i2d                              0x080036c1   Thumb Code    34  dflti.o(.text)
    __aeabi_f2d                              0x080036e3   Thumb Code    38  f2d.o(.text)
    __aeabi_d2f                              0x08003709   Thumb Code    56  d2f.o(.text)
    __aeabi_uidiv                            0x08003741   Thumb Code     0  uidiv.o(.text)
    __aeabi_uidivmod                         0x08003741   Thumb Code    44  uidiv.o(.text)
    __aeabi_uldivmod                         0x0800376d   Thumb Code    98  uldiv.o(.text)
    __aeabi_llsl                             0x080037cf   Thumb Code    30  llshl.o(.text)
    _ll_shift_l                              0x080037cf   Thumb Code     0  llshl.o(.text)
    __aeabi_lasr                             0x080037ed   Thumb Code    36  llsshr.o(.text)
    _ll_sshift_r                             0x080037ed   Thumb Code     0  llsshr.o(.text)
    __I$use$fp                               0x08003811   Thumb Code     0  iusefp.o(.text)
    _float_round                             0x08003811   Thumb Code    18  fepilogue.o(.text)
    _float_epilogue                          0x08003823   Thumb Code    92  fepilogue.o(.text)
    _double_round                            0x0800387f   Thumb Code    30  depilogue.o(.text)
    _double_epilogue                         0x0800389d   Thumb Code   156  depilogue.o(.text)
    __aeabi_d2ulz                            0x08003939   Thumb Code    48  dfixul.o(.text)
    __aeabi_cdrcmple                         0x08003969   Thumb Code    48  cdrcmple.o(.text)
    __scatterload                            0x08003999   Thumb Code    28  init.o(.text)
    __scatterload_rt2                        0x08003999   Thumb Code     0  init.o(.text)
    __aeabi_llsr                             0x080039bd   Thumb Code    32  llushr.o(.text)
    _ll_ushift_r                             0x080039bd   Thumb Code     0  llushr.o(.text)
    __0sprintf                               0x080039dd   Thumb Code    34  printfa.o(i.__0sprintf)
    __1sprintf                               0x080039dd   Thumb Code     0  printfa.o(i.__0sprintf)
    __2sprintf                               0x080039dd   Thumb Code     0  printfa.o(i.__0sprintf)
    __c89sprintf                             0x080039dd   Thumb Code     0  printfa.o(i.__0sprintf)
    sprintf                                  0x080039dd   Thumb Code     0  printfa.o(i.__0sprintf)
    __scatterload_copy                       0x08003a05   Thumb Code    14  handlers.o(i.__scatterload_copy)
    __scatterload_null                       0x08003a13   Thumb Code     2  handlers.o(i.__scatterload_null)
    __scatterload_zeroinit                   0x08003a15   Thumb Code    14  handlers.o(i.__scatterload_zeroinit)
    OLED_F8x16                               0x080042b8   Data        1520  oled.o(.constdata)
    Region$$Table$$Base                      0x080048a8   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080048c8   Number         0  anon$$obj.o(Region$$Table)
    key                                      0x20000000   Data           4  main.o(.data)
    w                                        0x20000004   Data           4  main.o(.data)
    SystemCoreClock                          0x20000008   Data           4  system_stm32f4xx.o(.data)
    AHBPrescTable                            0x2000000c   Data          16  system_stm32f4xx.o(.data)
    Flag                                     0x2000001c   Data           4  led.o(.data)
    turn_flag                                0x20000020   Data           4  timer.o(.data)
    cross_master_num                         0x20000024   Data           4  timer.o(.data)
    Timeout                                  0x2000002c   Data           4  timer.o(.data)
    Speed1                                   0x20000030   Data           4  encoder.o(.data)
    Speed2                                   0x20000034   Data           4  encoder.o(.data)
    Encoder1Count                            0x20000038   Data           4  encoder.o(.data)
    Encoder2Count                            0x2000003c   Data           4  encoder.o(.data)
    line                                     0x20000042   Data           2  serial.o(.data)
    arm                                      0x20000044   Data           2  serial.o(.data)
    cross_flag                               0x20000048   Data           4  serial.o(.data)
    num                                      0x2000004c   Data           4  serial.o(.data)
    thr                                      0x20000054   Data           4  serial.o(.data)
    cross                                    0x20000058   Data           4  serial.o(.data)
    stop                                     0x2000005c   Data           4  serial.o(.data)
    stop_flag                                0x20000060   Data           4  serial.o(.data)
    filt_err                                 0x20000064   Data           4  pid.o(.data)
    last_filt_err                            0x20000068   Data           4  pid.o(.data)
    modenow                                  0x20000070   Data           4  key.o(.data)
    overcount                                0x20000074   Data           4  hc_sr04.o(.data)
    OledString                               0x2000008c   Data         100  main.o(.bss)
    maixcam_data                             0x200000f0   Data          15  serial.o(.bss)
    pidMotor1Speed                           0x20000100   Data          32  pid.o(.bss)
    pidMotor2Speed                           0x20000120   Data          32  pid.o(.bss)
    pidTurnSpeed                             0x20000140   Data          32  pid.o(.bss)
    pidLocationSpeed                         0x20000160   Data          32  pid.o(.bss)
    __initial_sp                             0x20000580   Data           0  startup_stm32f40_41xxx.o(STACK)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000189

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00004954, Max: 0x00100000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000048c8, Max: 0x00100000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000188   Data   RO          809    RESET               startup_stm32f40_41xxx.o
    0x08000188   0x08000188   0x00000000   Code   RO          962  * .ARM.Collect$$$$00000000  mc_w.l(entry.o)
    0x08000188   0x08000188   0x00000004   Code   RO         1295    .ARM.Collect$$$$00000001  mc_w.l(entry2.o)
    0x0800018c   0x0800018c   0x00000004   Code   RO         1298    .ARM.Collect$$$$00000004  mc_w.l(entry5.o)
    0x08000190   0x08000190   0x00000000   Code   RO         1300    .ARM.Collect$$$$00000008  mc_w.l(entry7b.o)
    0x08000190   0x08000190   0x00000000   Code   RO         1302    .ARM.Collect$$$$0000000A  mc_w.l(entry8b.o)
    0x08000190   0x08000190   0x00000008   Code   RO         1303    .ARM.Collect$$$$0000000B  mc_w.l(entry9a.o)
    0x08000198   0x08000198   0x00000004   Code   RO         1310    .ARM.Collect$$$$0000000E  mc_w.l(entry12b.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         1305    .ARM.Collect$$$$0000000F  mc_w.l(entry10a.o)
    0x0800019c   0x0800019c   0x00000000   Code   RO         1307    .ARM.Collect$$$$00000011  mc_w.l(entry11a.o)
    0x0800019c   0x0800019c   0x00000004   Code   RO         1296    .ARM.Collect$$$$00002712  mc_w.l(entry2.o)
    0x080001a0   0x080001a0   0x00000218   Code   RO            3    .text               main.o
    0x080003b8   0x080003b8   0x00000012   Code   RO          176    .text               stm32f4xx_it.o
    0x080003ca   0x080003ca   0x00000002   PAD
    0x080003cc   0x080003cc   0x00000190   Code   RO          225    .text               system_stm32f4xx.o
    0x0800055c   0x0800055c   0x00000088   Code   RO          251    .text               led.o
    0x080005e4   0x080005e4   0x00000278   Code   RO          274    .text               timer.o
    0x0800085c   0x0800085c   0x00000060   Code   RO          302    .text               control.o
    0x080008bc   0x080008bc   0x00000240   Code   RO          322    .text               encoder.o
    0x08000afc   0x08000afc   0x000001b0   Code   RO          345    .text               motor.o
    0x08000cac   0x08000cac   0x00000468   Code   RO          379    .text               oled.o
    0x08001114   0x08001114   0x00000210   Code   RO          405    .text               pwm.o
    0x08001324   0x08001324   0x0000048c   Code   RO          428    .text               serial.o
    0x080017b0   0x080017b0   0x000001f8   Code   RO          484    .text               pid.o
    0x080019a8   0x080019a8   0x000000f0   Code   RO          502    .text               key.o
    0x08001a98   0x08001a98   0x00000184   Code   RO          654    .text               hc_sr04.o
    0x08001c1c   0x08001c1c   0x000000b8   Code   RO          737    .text               delay.o
    0x08001cd4   0x08001cd4   0x00000024   Code   RO          810    .text               startup_stm32f40_41xxx.o
    0x08001cf8   0x08001cf8   0x000000bc   Code   RO          816    .text               misc.o
    0x08001db4   0x08001db4   0x00000264   Code   RO          836    .text               stm32f4xx_gpio.o
    0x08002018   0x08002018   0x000004e0   Code   RO          856    .text               stm32f4xx_rcc.o
    0x080024f8   0x080024f8   0x00000ac2   Code   RO          898    .text               stm32f4xx_tim.o
    0x08002fba   0x08002fba   0x00000002   PAD
    0x08002fbc   0x08002fbc   0x000003f4   Code   RO          918    .text               stm32f4xx_usart.o
    0x080033b0   0x080033b0   0x0000014e   Code   RO         1232    .text               mf_w.l(dadd.o)
    0x080034fe   0x080034fe   0x000000e4   Code   RO         1234    .text               mf_w.l(dmul.o)
    0x080035e2   0x080035e2   0x000000de   Code   RO         1236    .text               mf_w.l(ddiv.o)
    0x080036c0   0x080036c0   0x00000022   Code   RO         1238    .text               mf_w.l(dflti.o)
    0x080036e2   0x080036e2   0x00000026   Code   RO         1240    .text               mf_w.l(f2d.o)
    0x08003708   0x08003708   0x00000038   Code   RO         1242    .text               mf_w.l(d2f.o)
    0x08003740   0x08003740   0x0000002c   Code   RO         1311    .text               mc_w.l(uidiv.o)
    0x0800376c   0x0800376c   0x00000062   Code   RO         1313    .text               mc_w.l(uldiv.o)
    0x080037ce   0x080037ce   0x0000001e   Code   RO         1315    .text               mc_w.l(llshl.o)
    0x080037ec   0x080037ec   0x00000024   Code   RO         1317    .text               mc_w.l(llsshr.o)
    0x08003810   0x08003810   0x00000000   Code   RO         1326    .text               mc_w.l(iusefp.o)
    0x08003810   0x08003810   0x0000006e   Code   RO         1327    .text               mf_w.l(fepilogue.o)
    0x0800387e   0x0800387e   0x000000ba   Code   RO         1329    .text               mf_w.l(depilogue.o)
    0x08003938   0x08003938   0x00000030   Code   RO         1333    .text               mf_w.l(dfixul.o)
    0x08003968   0x08003968   0x00000030   Code   RO         1337    .text               mf_w.l(cdrcmple.o)
    0x08003998   0x08003998   0x00000024   Code   RO         1339    .text               mc_w.l(init.o)
    0x080039bc   0x080039bc   0x00000020   Code   RO         1341    .text               mc_w.l(llushr.o)
    0x080039dc   0x080039dc   0x00000028   Code   RO         1206    i.__0sprintf        mc_w.l(printfa.o)
    0x08003a04   0x08003a04   0x0000000e   Code   RO         1347    i.__scatterload_copy  mc_w.l(handlers.o)
    0x08003a12   0x08003a12   0x00000002   Code   RO         1348    i.__scatterload_null  mc_w.l(handlers.o)
    0x08003a14   0x08003a14   0x0000000e   Code   RO         1349    i.__scatterload_zeroinit  mc_w.l(handlers.o)
    0x08003a22   0x08003a22   0x00000002   PAD
    0x08003a24   0x08003a24   0x00000184   Code   RO         1211    i._fp_digits        mc_w.l(printfa.o)
    0x08003ba8   0x08003ba8   0x000006b4   Code   RO         1212    i._printf_core      mc_w.l(printfa.o)
    0x0800425c   0x0800425c   0x00000024   Code   RO         1213    i._printf_post_padding  mc_w.l(printfa.o)
    0x08004280   0x08004280   0x0000002e   Code   RO         1214    i._printf_pre_padding  mc_w.l(printfa.o)
    0x080042ae   0x080042ae   0x0000000a   Code   RO         1216    i._sputc            mc_w.l(printfa.o)
    0x080042b8   0x080042b8   0x000005f0   Data   RO          380    .constdata          oled.o
    0x080048a8   0x080048a8   0x00000020   Data   RO         1345    Region$$Table       anon$$obj.o


    Execution Region RW_IRAM2 (Exec base: 0x10000000, Load base: 0x08004954, Size: 0x00000000, Max: 0x00010000, ABSOLUTE)

    **** No section assigned to this execution region ****


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080048c8, Size: 0x00000580, Max: 0x00020000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080048c8   0x00000008   Data   RW            5    .data               main.o
    0x20000008   0x080048d0   0x00000014   Data   RW          226    .data               system_stm32f4xx.o
    0x2000001c   0x080048e4   0x00000004   Data   RW          252    .data               led.o
    0x20000020   0x080048e8   0x00000010   Data   RW          276    .data               timer.o
    0x20000030   0x080048f8   0x00000010   Data   RW          323    .data               encoder.o
    0x20000040   0x08004908   0x00000024   Data   RW          430    .data               serial.o
    0x20000064   0x0800492c   0x00000008   Data   RW          486    .data               pid.o
    0x2000006c   0x08004934   0x00000008   Data   RW          503    .data               key.o
    0x20000074   0x0800493c   0x00000004   Data   RW          655    .data               hc_sr04.o
    0x20000078   0x08004940   0x00000004   Data   RW          738    .data               delay.o
    0x2000007c   0x08004944   0x00000010   Data   RW          857    .data               stm32f4xx_rcc.o
    0x2000008c        -       0x00000064   Zero   RW            4    .bss                main.o
    0x200000f0        -       0x0000000f   Zero   RW          429    .bss                serial.o
    0x200000ff   0x08004954   0x00000001   PAD
    0x20000100        -       0x00000080   Zero   RW          485    .bss                pid.o
    0x20000180        -       0x00000400   Zero   RW          807    STACK               startup_stm32f40_41xxx.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

        96         10          0          0          0        571   control.o
       184          4          0          4          0       1505   delay.o
       576         28          0         16          0       1738   encoder.o
       388         28          0          4          0       1508   hc_sr04.o
       240          8          0          8          0       1138   key.o
       136         22          0          4          0        878   led.o
       536         60          0          8        100     284029   main.o
       188         14          0          0          0       2009   misc.o
       432         40          0          0          0       1450   motor.o
      1128          8       1520          0          0       6330   oled.o
       504         52          0          8        128       2488   pid.o
       528         28          0          0          0       1416   pwm.o
      1164         48          0         36         15       5037   serial.o
        36          8        392          0       1024        820   startup_stm32f40_41xxx.o
       612         44          0          0          0       4765   stm32f4xx_gpio.o
        18          0          0          0          0       1202   stm32f4xx_it.o
      1248         52          0         16          0      13728   stm32f4xx_rcc.o
      2754         60          0          0          0      24968   stm32f4xx_tim.o
      1012         32          0          0          0       8792   stm32f4xx_usart.o
       400         36          0         20          0       1883   system_stm32f4xx.o
       632         40          0         16          0       1642   timer.o

    ----------------------------------------------------------------------
     12816        <USER>       <GROUP>        140       1268     367897   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         4          0          0          0          1          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

         0          0          0          0          0          0   entry.o
         0          0          0          0          0          0   entry10a.o
         0          0          0          0          0          0   entry11a.o
         4          0          0          0          0          0   entry12b.o
         8          4          0          0          0          0   entry2.o
         4          0          0          0          0          0   entry5.o
         0          0          0          0          0          0   entry7b.o
         0          0          0          0          0          0   entry8b.o
         8          4          0          0          0          0   entry9a.o
        30          0          0          0          0          0   handlers.o
        36          8          0          0          0         68   init.o
         0          0          0          0          0          0   iusefp.o
        30          0          0          0          0         68   llshl.o
        36          0          0          0          0         68   llsshr.o
        32          0          0          0          0         68   llushr.o
      2236         86          0          0          0        532   printfa.o
        44          0          0          0          0         80   uidiv.o
        98          0          0          0          0         92   uldiv.o
        48          0          0          0          0         68   cdrcmple.o
        56          0          0          0          0         88   d2f.o
       334          0          0          0          0        148   dadd.o
       222          0          0          0          0        100   ddiv.o
       186          0          0          0          0        176   depilogue.o
        48          0          0          0          0         68   dfixul.o
        34          0          0          0          0         76   dflti.o
       228          0          0          0          0         96   dmul.o
        38          0          0          0          0         68   f2d.o
       110          0          0          0          0        168   fepilogue.o

    ----------------------------------------------------------------------
      3872        <USER>          <GROUP>          0          0       2032   Library Totals
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      2566        102          0          0          0        976   mc_w.l
      1304          0          0          0          0       1056   mf_w.l

    ----------------------------------------------------------------------
      3872        <USER>          <GROUP>          0          0       2032   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     16688        724       1944        140       1268     365645   Grand Totals
     16688        724       1944        140       1268     365645   ELF Image Totals
     16688        724       1944        140          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                18632 (  18.20kB)
    Total RW  Size (RW Data + ZI Data)              1408 (   1.38kB)
    Total ROM Size (Code + RO Data + RW Data)      18772 (  18.33kB)

==============================================================================

