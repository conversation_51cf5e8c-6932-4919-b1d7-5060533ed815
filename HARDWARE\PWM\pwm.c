#include "pwm.h"
#include "led.h"

 
//////////////////////////////////////////////////////////////////////////////////	 
//本程序只供学习使用，未经作者许可，不得用于其它任何用途
//ALIENTEK STM32F407开发板
//定时器PWM 驱动代码	   
//正点原子@ALIENTEK
//技术论坛:www.openedv.com
//创建日期:2014/5/4
//版本：V1.0
//版权所有，盗版必究。
//Copyright(C) 广州市星翼电子科技有限公司 2014-2024
//All rights reserved									  
////////////////////////////////////////////////////////////////////////////////// 	 


//TIM14 PWM部分初始化 
//PWM输出初始化
//arr：自动重装值
//psc：时钟预分频数
void TIM1_PWM_Init(u32 arr,u32 psc)//电机用
{		 					 
	//此部分需手动修改IO口设置
	
	GPIO_InitTypeDef GPIO_InitStructure;
	TIM_TimeBaseInitTypeDef  TIM_TimeBaseStructure;
	TIM_OCInitTypeDef  TIM_OCInitStructure;
	
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM1,ENABLE);  	  //TIM14时钟使能    
	RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOE, ENABLE); 	//使能PORTE时钟	
	
	GPIO_PinAFConfig(GPIOE,GPIO_PinSource9,GPIO_AF_TIM1);  //GPIOE9复用为定时器14
	GPIO_PinAFConfig(GPIOE,GPIO_PinSource11,GPIO_AF_TIM1); //GPIOE11复用为定时器14
	GPIO_PinAFConfig(GPIOE,GPIO_PinSource13,GPIO_AF_TIM1); //GPIOE13复用为定时器14
	GPIO_PinAFConfig(GPIOE,GPIO_PinSource14,GPIO_AF_TIM1); //GPIOE14复用为定时器14
	
	GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9|GPIO_Pin_11|GPIO_Pin_13|GPIO_Pin_14; //GPIOE
	GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF;                                  //复用功能
	GPIO_InitStructure.GPIO_Speed = GPIO_Speed_100MHz;	                          //速度100MHz
	GPIO_InitStructure.GPIO_OType = GPIO_OType_PP;                                //推挽复用输出
	GPIO_InitStructure.GPIO_PuPd = GPIO_PuPd_UP;                                  //上拉
	GPIO_Init(GPIOE,&GPIO_InitStructure);                                         //初始化PF9
	  
	
	TIM_TimeBaseStructure.TIM_Prescaler=psc;  //定时器分频
	TIM_TimeBaseStructure.TIM_CounterMode=TIM_CounterMode_Up; //向上计数模式
	TIM_TimeBaseStructure.TIM_Period=arr;   //自动重装载值
	TIM_TimeBaseStructure.TIM_ClockDivision=TIM_CKD_DIV1; 
	TIM_TimeBaseInit(TIM1,&TIM_TimeBaseStructure);//初始化定时器14
	
	//初始化TIM14 Channel1 PWM模式	 
	TIM_OCInitStructure.TIM_OCMode = TIM_OCMode_PWM1; //选择定时器模式:TIM脉冲宽度调制模式2
 	TIM_OCInitStructure.TIM_OutputState = TIM_OutputState_Enable; //比较输出使能
	TIM_OCInitStructure.TIM_OCPolarity = TIM_OCPolarity_High; //输出极性:TIM输出比较极性低
	TIM_OCInitStructure.TIM_OCIdleState = TIM_OCIdleState_Reset;//在空闲时输出     低,这里的设置可以改变TIM_OCPolarity 如果没这句，第1通道有问题
//	TIM_OCInitStructure.TIM_Pulse =50;
	
//	TIM_OC1Init(TIM1, &TIM_OCInitStructure);  //根据T指定的参数初始化外设TIM1 4OC1
//	TIM_OC2Init(TIM1, &TIM_OCInitStructure);  //根据T指定的参数初始化外设TIM1 4OC1
	TIM_OC3Init(TIM1, &TIM_OCInitStructure);  //根据T指定的参数初始化外设TIM1 4OC1
	TIM_OC4Init(TIM1, &TIM_OCInitStructure);  //根据T指定的参数初始化外设TIM1 4OC1

//	TIM_OC1PreloadConfig(TIM1, TIM_OCPreload_Enable);  //使能TIM14在CCR1上的预装载寄存器
//	TIM_OC2PreloadConfig(TIM1, TIM_OCPreload_Enable);  //使能TIM14在CCR1上的预装载寄存器
	TIM_OC3PreloadConfig(TIM1, TIM_OCPreload_Enable);  //使能TIM14在CCR1上的预装载寄存器
	TIM_OC4PreloadConfig(TIM1, TIM_OCPreload_Enable);  //使能TIM14在CCR1上的预装载寄存器
 
  TIM_ARRPreloadConfig(TIM1,ENABLE);//ARPE使能 
	
	TIM_Cmd(TIM1, ENABLE);  //使能TIM14
	
	TIM_CtrlPWMOutputs(TIM1,ENABLE);
 
  TIM_SetCompare3(TIM1,0); //先置零占空比
  TIM_SetCompare4(TIM1,0);
 
	}




void TIM9_PWM_Init(void)	//舵机用
{
    //使能GPIOE,GPIOA和TIM9的时钟
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOE, ENABLE);
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_TIM9, ENABLE);

    //配置PE5引脚为复用功能
    GPIO_InitTypeDef GPIOE_InitStruct;
    GPIOE_InitStruct.GPIO_Mode = GPIO_Mode_AF;
    GPIOE_InitStruct.GPIO_Speed = GPIO_Speed_100MHz;
    GPIOE_InitStruct.GPIO_OType = GPIO_OType_PP;
    GPIOE_InitStruct.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIOE_InitStruct.GPIO_Pin = GPIO_Pin_5;
    GPIO_Init(GPIOE,&GPIOE_InitStruct);

    //配置PA3引脚为复用功能
    GPIO_InitTypeDef GPIOA_InitStruct;  
    GPIOA_InitStruct.GPIO_Mode = GPIO_Mode_AF;
    GPIOA_InitStruct.GPIO_Speed = GPIO_Speed_100MHz;
    GPIOA_InitStruct.GPIO_OType = GPIO_OType_PP;
    GPIOA_InitStruct.GPIO_PuPd = GPIO_PuPd_NOPULL;
    GPIOA_InitStruct.GPIO_Pin = GPIO_Pin_3;
    GPIO_Init(GPIOA,&GPIOA_InitStruct);

    //引脚映射至TIM9
    GPIO_PinAFConfig(GPIOE,GPIO_PinSource5,GPIO_AF_TIM9);  //GPIOE5复用为定时器9
	GPIO_PinAFConfig(GPIOA,GPIO_PinSource3,GPIO_AF_TIM9);  //GPIOA3复用为定时器9

    //配置TIM9
    TIM_TimeBaseInitTypeDef TIM_TimeBaseStruct;
    TIM_TimeBaseStruct.TIM_Period = 20000 - 1; // 自动重装载值
    TIM_TimeBaseStruct.TIM_Prescaler = 168 - 1; // 预分频器1000000
    TIM_TimeBaseStruct.TIM_ClockDivision = TIM_CKD_DIV1;
    TIM_TimeBaseStruct.TIM_CounterMode = TIM_CounterMode_Up;
    TIM_TimeBaseInit(TIM9, &TIM_TimeBaseStruct);

    //配置TIM9的PWM模式
    TIM_OCInitTypeDef TIM_OCInitStruct;
    TIM_OCInitStruct.TIM_OCMode = TIM_OCMode_PWM1;
    TIM_OCInitStruct.TIM_OutputState = TIM_OutputState_Enable;
    TIM_OCInitStruct.TIM_OCPolarity = TIM_OCPolarity_High;
//  TIM_OCInitStruct.TIM_Pulse = 0;

    TIM_OC1Init(TIM9, &TIM_OCInitStruct);  //根据T指定的参数初始化外设TIM9 OC1
	TIM_OC2Init(TIM9, &TIM_OCInitStruct);  //根据T指定的参数初始化外设TIM9 OC2

    TIM_OC1PreloadConfig(TIM9, TIM_OCPreload_Enable);  //使能TIM9在CCR1上的预装载寄存器
	TIM_OC2PreloadConfig(TIM9, TIM_OCPreload_Enable);  //使能TIM9在CCR1上的预装载寄存器

	//使能TIM9的预装载寄存器
	TIM_ARRPreloadConfig(TIM9,ENABLE);//ARPE使能

	//使能TIM9
	TIM_Cmd(TIM9, ENABLE);

	//使能TIM1的主输出
	TIM_CtrlPWMOutputs(TIM9, ENABLE);

	//设置占空比先置零占空比
	TIM_SetCompare1(TIM9,0); 
	TIM_SetCompare2(TIM9,0);
}

//270°舵机控制
void Servo_Set (float Angle)	//摄像头方向控制舵机
{
	unsigned short Pulse;
	
	
	Pulse =(Angle / 270 * 2000) + 500;

	TIM_SetCompare2(TIM9,Pulse);
}
